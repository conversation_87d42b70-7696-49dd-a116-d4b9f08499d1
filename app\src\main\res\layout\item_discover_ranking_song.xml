<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp">

    <ImageView
        android:id="@+id/ivCover"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:scaleType="fitXY"
        android:src="@drawable/ic_default_cover" />

    <TextView
        android:id="@+id/tvRank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="14dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="@color/common_text_h1_color"
        android:textSize="16dp"
        android:textStyle="bold"
        tools:text="1" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/common_text_h1_color"
            android:textSize="16dp"
            android:textStyle="bold"
            tools:text="歌曲" />

        <TextView
            android:id="@+id/tvSubTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="@color/grey"
            android:textSize="12sp"
            tools:text="歌手" />
    </LinearLayout>
</LinearLayout>