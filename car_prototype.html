<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>波尼音乐 - Android Automotive 车载版原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Roboto', sans-serif;
            background: #1a1a1a;
            color: #FFFFFF;
            overflow: hidden;
            height: 100vh;
            width: 100vw;
        }

        .car-screen {
            width: 100vw;
            height: 100vh;
            display: flex;
            position: relative;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
        }

        /* 左侧导航栏 */
        .left-sidebar {
            width: 280px;
            background: #2d2d2d;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #404040;
        }

        .app-header {
            height: 80px;
            display: flex;
            align-items: center;
            padding: 0 24px;
            border-bottom: 1px solid #404040;
            background: #1a1a1a;
        }

        .app-logo {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #F44336, #D32F2F);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 12px;
        }

        .app-title {
            font-size: 18px;
            font-weight: 600;
            color: #FFFFFF;
        }

        .nav-section {
            flex: 1;
            padding: 24px 16px;
            overflow-y: auto;
        }

        .nav-group {
            margin-bottom: 32px;
        }

        .nav-group-title {
            font-size: 12px;
            font-weight: 600;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 16px;
            padding: 0 8px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            margin: 4px 0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #ccc;
            font-size: 14px;
        }

        .nav-item:hover {
            background: #404040;
            color: #fff;
        }

        .nav-item.active {
            background: linear-gradient(135deg, #F44336, #D32F2F);
            color: #fff;
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            background: currentColor;
            border-radius: 4px;
            opacity: 0.8;
        }

        /* 中间内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1a1a1a;
        }

        .content-header {
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 32px;
            border-bottom: 1px solid #404040;
            background: #2d2d2d;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #fff;
        }

        .header-controls {
            display: flex;
            gap: 16px;
        }

        .search-box {
            width: 300px;
            height: 40px;
            background: #404040;
            border: 1px solid #555;
            border-radius: 20px;
            padding: 0 16px;
            color: #fff;
            font-size: 14px;
            outline: none;
        }

        .search-box:focus {
            border-color: #F44336;
            box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
        }

        .content-body {
            flex: 1;
            padding: 24px 32px;
            overflow-y: auto;
        }

        /* 音乐列表 */
        .music-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-top: 24px;
        }

        .music-card {
            background: #2d2d2d;
            border-radius: 16px;
            padding: 20px;
            border: 1px solid #404040;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .music-card:hover {
            background: #404040;
            border-color: #555;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
        }

        .music-card-cover {
            width: 100%;
            height: 160px;
            background: linear-gradient(135deg, #F44336, #D32F2F);
            border-radius: 12px;
            margin-bottom: 16px;
            position: relative;
            overflow: hidden;
        }

        .music-card-cover::before {
            content: '♪';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 48px;
            color: rgba(255, 255, 255, 0.8);
        }

        .music-card-title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
            margin-bottom: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .music-card-artist {
            font-size: 14px;
            color: #aaa;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 右侧播放面板 */
        .right-player {
            width: 400px;
            background: #2d2d2d;
            border-left: 1px solid #404040;
            display: flex;
            flex-direction: column;
        }

        .player-header {
            height: 80px;
            display: flex;
            align-items: center;
            padding: 0 24px;
            border-bottom: 1px solid #404040;
            background: #1a1a1a;
        }

        .player-title {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
        }

        .player-body {
            flex: 1;
            padding: 32px 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .album-cover {
            width: 280px;
            height: 280px;
            border-radius: 20px;
            background: linear-gradient(135deg, #F44336, #D32F2F);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
            position: relative;
            overflow: hidden;
            margin-bottom: 32px;
            border: 3px solid #404040;
        }

        .album-cover::before {
            content: '♪';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 64px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: bold;
        }

        .vinyl-animation {
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border: 2px solid rgba(244, 67, 54, 0.4);
            border-radius: 50%;
            animation: rotate 8s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .song-info {
            text-align: center;
            margin-bottom: 32px;
            width: 100%;
        }

        .song-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #FFFFFF;
            line-height: 1.3;
        }

        .song-artist {
            font-size: 16px;
            color: #aaa;
            margin-bottom: 24px;
        }

        .song-progress {
            width: 100%;
            height: 6px;
            background: #404040;
            border-radius: 3px;
            margin-bottom: 12px;
            overflow: hidden;
            cursor: pointer;
        }

        .song-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #F44336, #D32F2F);
            width: 35%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .song-time {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #888;
            margin-bottom: 32px;
        }

        .controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 32px;
        }

        .control-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #404040;
            border: 1px solid #555;
            color: #FFFFFF;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn.play {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #F44336, #D32F2F);
            border-color: #F44336;
            font-size: 24px;
            box-shadow: 0 4px 16px rgba(244, 67, 54, 0.4);
        }

        .control-btn:hover {
            background: #555;
            border-color: #777;
        }

        .control-btn.play:hover {
            box-shadow: 0 6px 20px rgba(244, 67, 54, 0.6);
        }

        .control-btn.favorite.active {
            color: #F44336;
            border-color: #F44336;
        }

        /* 播放队列 */
        .play-queue {
            flex: 1;
            padding: 0 24px 24px;
            overflow-y: auto;
        }

        .queue-title {
            font-size: 16px;
            font-weight: 600;
            color: #fff;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #404040;
        }

        .queue-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            cursor: pointer;
            border-radius: 8px;
            transition: background 0.3s ease;
        }

        .queue-item:hover {
            background: #404040;
        }

        .queue-item.current {
            background: rgba(244, 67, 54, 0.1);
            border-left: 3px solid #F44336;
            padding-left: 9px;
        }

        .queue-cover {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #F44336, #D32F2F);
            border-radius: 8px;
            margin-right: 12px;
            position: relative;
        }

        .queue-cover::before {
            content: '♪';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
        }

        .queue-info {
            flex: 1;
            min-width: 0;
        }

        .queue-title-text {
            font-size: 14px;
            color: #fff;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .queue-artist {
            font-size: 12px;
            color: #aaa;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .lyrics-section {
            flex: 1;
            padding: 24px;
            background: #424242;
            border-radius: 16px;
            overflow-y: auto;
            border: 1px solid #616161;
            margin-left: 16px;
        }

        .lyrics-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #616161;
        }

        .lyrics-title {
            font-size: 18px;
            font-weight: 600;
            color: #FFFFFF;
        }

        .sound-wave {
            width: 32px;
            height: 20px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 20"><rect x="2" y="8" width="2" height="4" fill="%23F44336"><animate attributeName="height" values="4;12;4" dur="1s" repeatCount="indefinite"/><animate attributeName="y" values="8;4;8" dur="1s" repeatCount="indefinite"/></rect><rect x="8" y="6" width="2" height="8" fill="%23F44336"><animate attributeName="height" values="8;16;8" dur="1.2s" repeatCount="indefinite"/><animate attributeName="y" values="6;2;6" dur="1.2s" repeatCount="indefinite"/></rect><rect x="14" y="4" width="2" height="12" fill="%23F44336"><animate attributeName="height" values="12;20;12" dur="0.8s" repeatCount="indefinite"/><animate attributeName="y" values="4;0;4" dur="0.8s" repeatCount="indefinite"/></rect><rect x="20" y="6" width="2" height="8" fill="%23F44336"><animate attributeName="height" values="8;14;8" dur="1.1s" repeatCount="indefinite"/><animate attributeName="y" values="6;3;6" dur="1.1s" repeatCount="indefinite"/></rect><rect x="26" y="8" width="2" height="4" fill="%23F44336"><animate attributeName="height" values="4;10;4" dur="0.9s" repeatCount="indefinite"/><animate attributeName="y" values="8;5;8" dur="0.9s" repeatCount="indefinite"/></rect></svg>') no-repeat center;
            background-size: contain;
        }

        .lyric-line {
            padding: 12px 0;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            cursor: pointer;
            color: #9E9E9E;
            border-radius: 8px;
            margin: 4px 0;
        }

        .lyric-line.current {
            color: #F44336;
            font-weight: 600;
            font-size: 18px;
            background: rgba(244, 67, 54, 0.1);
            padding: 16px;
        }

        .lyric-line:hover {
            color: #FFFFFF;
            background: rgba(255, 255, 255, 0.1);
        }

        /* 音乐列表界面 */
        .content-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #424242;
        }

        .content-title {
            font-size: 24px;
            font-weight: 600;
            color: #FFFFFF;
        }

        .view-toggle {
            display: flex;
            background: #424242;
            border-radius: 8px;
            padding: 4px;
        }

        .view-toggle-btn {
            padding: 8px 16px;
            background: transparent;
            border: none;
            color: #9E9E9E;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .view-toggle-btn.active {
            background: #F44336;
            color: #FFFFFF;
        }

        .music-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 16px;
            padding: 16px 0;
        }

        .music-item {
            background: #424242;
            border-radius: 16px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 16px;
            border: 1px solid transparent;
        }

        .music-item:hover {
            background: #616161;
            border-color: #9E9E9E;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .music-item-cover {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            background: linear-gradient(135deg, #F44336, #D32F2F);
            flex-shrink: 0;
            position: relative;
            overflow: hidden;
        }

        .music-item-cover::before {
            content: '♪';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: rgba(255, 255, 255, 0.8);
            font-size: 20px;
        }

        .music-item-info {
            flex: 1;
            min-width: 0;
        }

        .music-item-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #FFFFFF;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .music-item-artist {
            font-size: 14px;
            color: #9E9E9E;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .music-item-actions {
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .music-item:hover .music-item-actions {
            opacity: 1;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #616161;
            border: none;
            color: #FFFFFF;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .action-btn:hover {
            background: #F44336;
        }

        /* 搜索界面 */
        .search-section {
            margin-bottom: 24px;
        }

        .search-input {
            width: 100%;
            padding: 16px 20px;
            font-size: 16px;
            border: 2px solid #616161;
            border-radius: 16px;
            background: #424242;
            color: #FFFFFF;
            outline: none;
            transition: all 0.3s;
        }

        .search-input:focus {
            border-color: #F44336;
            box-shadow: 0 0 0 4px rgba(244, 67, 54, 0.2);
        }

        .search-input::placeholder {
            color: #9E9E9E;
        }

        .search-tag {
            padding: 8px 16px;
            background: #424242;
            border: 1px solid #616161;
            border-radius: 20px;
            color: #FFFFFF;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }

        .search-tag:hover {
            background: #F44336;
            border-color: #F44336;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 250px;
            }

            .album-cover {
                width: 280px;
                height: 280px;
            }
        }

        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="car-screen">
        <!-- 左侧导航栏 -->
        <div class="left-sidebar">
            <div class="app-header">
                <div class="app-logo">🎵</div>
                <div class="app-title">波尼音乐</div>
            </div>
            <div class="nav-section">
                <div class="nav-group">
                    <div class="nav-group-title">发现音乐</div>
                    <div class="nav-item active" onclick="showScreen('discover')">
                        <div class="nav-icon"></div>
                        推荐
                    </div>
                    <div class="nav-item" onclick="showScreen('ranking')">
                        <div class="nav-icon"></div>
                        排行榜
                    </div>
                    <div class="nav-item" onclick="showScreen('playlist')">
                        <div class="nav-icon"></div>
                        歌单
                    </div>
                </div>
                <div class="nav-group">
                    <div class="nav-group-title">我的音乐</div>
                    <div class="nav-item" onclick="showScreen('local')">
                        <div class="nav-icon"></div>
                        本地音乐
                    </div>
                    <div class="nav-item" onclick="showScreen('favorite')">
                        <div class="nav-icon"></div>
                        我喜欢的
                    </div>
                    <div class="nav-item" onclick="showScreen('recent')">
                        <div class="nav-icon"></div>
                        最近播放
                    </div>
                    <div class="nav-item" onclick="showScreen('download')">
                        <div class="nav-icon"></div>
                        下载管理
                    </div>
                </div>
                <div class="nav-group">
                    <div class="nav-group-title">其他</div>
                    <div class="nav-item" onclick="showScreen('search')">
                        <div class="nav-icon"></div>
                        搜索
                    </div>
                    <div class="nav-item" onclick="showScreen('settings')">
                        <div class="nav-icon"></div>
                        设置
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间内容区域 -->
        <div class="main-content">
            <div class="content-header">
                <div class="page-title" id="page-title">推荐音乐</div>
                <div class="header-controls">
                    <input type="text" class="search-box" placeholder="搜索音乐..." />
                </div>
            </div>
            <div class="content-body" id="content-body">
                <!-- 动态内容区域 -->
                <div class="music-grid">
                    <div class="music-card" onclick="playMusic('夜空中最亮的星', '逃跑计划')">
                        <div class="music-card-cover"></div>
                        <div class="music-card-title">夜空中最亮的星</div>
                        <div class="music-card-artist">逃跑计划</div>
                    </div>
                    <div class="music-card" onclick="playMusic('成都', '赵雷')">
                        <div class="music-card-cover"></div>
                        <div class="music-card-title">成都</div>
                        <div class="music-card-artist">赵雷</div>
                    </div>
                    <div class="music-card" onclick="playMusic('演员', '薛之谦')">
                        <div class="music-card-cover"></div>
                        <div class="music-card-title">演员</div>
                        <div class="music-card-artist">薛之谦</div>
                    </div>
                    <div class="music-card" onclick="playMusic('告白气球', '周杰伦')">
                        <div class="music-card-cover"></div>
                        <div class="music-card-title">告白气球</div>
                        <div class="music-card-artist">周杰伦</div>
                    </div>
                    <div class="music-card" onclick="playMusic('稻香', '周杰伦')">
                        <div class="music-card-cover"></div>
                        <div class="music-card-title">稻香</div>
                        <div class="music-card-artist">周杰伦</div>
                    </div>
                    <div class="music-card" onclick="playMusic('青花瓷', '周杰伦')">
                        <div class="music-card-cover"></div>
                        <div class="music-card-title">青花瓷</div>
                        <div class="music-card-artist">周杰伦</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧播放面板 -->
        <div class="right-player">
            <div class="player-header">
                <div class="player-title">正在播放</div>
            </div>
            <div class="player-body">
                <div class="album-cover">
                    <div class="vinyl-animation"></div>
                </div>
                <div class="song-info">
                    <div class="song-title">夜空中最亮的星</div>
                    <div class="song-artist">逃跑计划</div>
                    <div class="song-progress">
                        <div class="song-progress-fill"></div>
                    </div>
                    <div class="song-time">
                        <span>1:23</span>
                        <span>4:12</span>
                    </div>
                </div>
                <div class="controls">
                    <button class="control-btn" title="上一首">⏮</button>
                    <button class="control-btn play" title="播放/暂停">▶</button>
                    <button class="control-btn" title="下一首">⏭</button>
                    <button class="control-btn" title="播放模式">🔀</button>
                    <button class="control-btn favorite" title="收藏">❤</button>
                </div>
            </div>
            <div class="play-queue">
                <div class="queue-title">播放队列</div>
                <div class="queue-item current">
                    <div class="queue-cover"></div>
                    <div class="queue-info">
                        <div class="queue-title-text">夜空中最亮的星</div>
                        <div class="queue-artist">逃跑计划</div>
                    </div>
                </div>
                <div class="queue-item">
                    <div class="queue-cover"></div>
                    <div class="queue-info">
                        <div class="queue-title-text">成都</div>
                        <div class="queue-artist">赵雷</div>
                    </div>
                </div>
                <div class="queue-item">
                    <div class="queue-cover"></div>
                    <div class="queue-info">
                        <div class="queue-title-text">演员</div>
                        <div class="queue-artist">薛之谦</div>
                    </div>
                </div>
                <div class="queue-item">
                    <div class="queue-cover"></div>
                    <div class="queue-info">
                        <div class="queue-title-text">告白气球</div>
                        <div class="queue-artist">周杰伦</div>
                    </div>
                </div>
                <div class="queue-item">
                    <div class="queue-cover"></div>
                    <div class="queue-info">
                        <div class="queue-title-text">稻香</div>
                        <div class="queue-artist">周杰伦</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showScreen(screenType) {
            // 更新导航栏状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // 更新页面标题和内容
            const pageTitle = document.getElementById('page-title');
            const contentBody = document.getElementById('content-body');

            loadContent(screenType, contentBody, pageTitle);
        }

        function loadContent(type, container, titleElement) {
            let content = '';
            let title = '';

            switch(type) {
                case 'discover':
                    title = '推荐音乐';
                    content = generateMusicGrid([
                        {title: '夜空中最亮的星', artist: '逃跑计划'},
                        {title: '成都', artist: '赵雷'},
                        {title: '演员', artist: '薛之谦'},
                        {title: '告白气球', artist: '周杰伦'},
                        {title: '稻香', artist: '周杰伦'},
                        {title: '青花瓷', artist: '周杰伦'}
                    ]);
                    break;
                case 'ranking':
                    title = '排行榜';
                    content = generateMusicGrid([
                        {title: '热歌榜第1名', artist: '周杰伦'},
                        {title: '热歌榜第2名', artist: '薛之谦'},
                        {title: '热歌榜第3名', artist: '逃跑计划'},
                        {title: '热歌榜第4名', artist: '赵雷'},
                        {title: '热歌榜第5名', artist: '毛不易'},
                        {title: '热歌榜第6名', artist: '李玉刚'}
                    ]);
                    break;
                case 'playlist':
                    title = '歌单广场';
                    content = generateMusicGrid([
                        {title: '华语经典', artist: '精选歌单'},
                        {title: '民谣时光', artist: '精选歌单'},
                        {title: '流行金曲', artist: '精选歌单'},
                        {title: '怀旧经典', artist: '精选歌单'},
                        {title: '网络热歌', artist: '精选歌单'},
                        {title: '影视金曲', artist: '精选歌单'}
                    ]);
                    break;
                case 'local':
                    title = '本地音乐';
                    content = generateMusicGrid([
                        {title: '夜空中最亮的星', artist: '逃跑计划'},
                        {title: '成都', artist: '赵雷'},
                        {title: '南山南', artist: '马頔'},
                        {title: '理想', artist: '赵雷'},
                        {title: '消愁', artist: '毛不易'},
                        {title: '像我这样的人', artist: '毛不易'}
                    ]);
                    break;
                case 'favorite':
                    title = '我喜欢的';
                    content = generateMusicGrid([
                        {title: '夜空中最亮的星', artist: '逃跑计划'},
                        {title: '告白气球', artist: '周杰伦'},
                        {title: '演员', artist: '薛之谦'}
                    ]);
                    break;
                case 'recent':
                    title = '最近播放';
                    content = generateMusicGrid([
                        {title: '夜空中最亮的星', artist: '逃跑计划'},
                        {title: '成都', artist: '赵雷'},
                        {title: '演员', artist: '薛之谦'}
                    ]);
                    break;
                case 'download':
                    title = '下载管理';
                    content = generateMusicGrid([
                        {title: '夜空中最亮的星', artist: '逃跑计划 - 已下载'},
                        {title: '成都', artist: '赵雷 - 已下载'}
                    ]);
                    break;
                case 'search':
                    title = '搜索';
                    content = `
                        <div class="search-suggestions" style="margin-bottom: 24px;">
                            <div style="color: #aaa; margin-bottom: 16px; font-size: 14px;">热门搜索</div>
                            <div style="display: flex; flex-wrap: wrap; gap: 12px;">
                                <span class="search-tag" onclick="searchFor('周杰伦')">周杰伦</span>
                                <span class="search-tag" onclick="searchFor('夜空中最亮的星')">夜空中最亮的星</span>
                                <span class="search-tag" onclick="searchFor('成都')">成都</span>
                                <span class="search-tag" onclick="searchFor('演员')">演员</span>
                            </div>
                        </div>
                        ${generateMusicGrid([
                            {title: '夜空中最亮的星', artist: '逃跑计划'},
                            {title: '成都', artist: '赵雷'},
                            {title: '演员', artist: '薛之谦'},
                            {title: '告白气球', artist: '周杰伦'}
                        ])}
                    `;
                    break;
                case 'settings':
                    title = '设置';
                    content = `
                        <div class="music-grid">
                            <div class="music-card">
                                <div class="music-card-title">音质设置</div>
                                <div class="music-card-artist">高品质 320kbps</div>
                            </div>
                            <div class="music-card">
                                <div class="music-card-title">夜间模式</div>
                                <div class="music-card-artist">已开启</div>
                            </div>
                            <div class="music-card">
                                <div class="music-card-title">API域名设置</div>
                                <div class="music-card-artist">localhost:3000</div>
                            </div>
                        </div>
                    `;
                    break;
            }

            if (titleElement) {
                titleElement.textContent = title;
            }
            container.innerHTML = content;
        }

        function generateMusicGrid(songs) {
            return `
                <div class="music-grid">
                    ${songs.map(song => `
                        <div class="music-card" onclick="playMusic('${song.title}', '${song.artist}')">
                            <div class="music-card-cover"></div>
                            <div class="music-card-title">${song.title}</div>
                            <div class="music-card-artist">${song.artist}</div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function playMusic(title, artist) {
            // 更新播放信息
            document.querySelector('.song-title').textContent = title;
            document.querySelector('.song-artist').textContent = artist;

            // 更新播放队列中的当前歌曲
            document.querySelectorAll('.queue-item').forEach(item => {
                item.classList.remove('current');
            });

            // 模拟添加到队列顶部
            const queueTitle = document.querySelector('.queue-title-text');
            const queueArtist = document.querySelector('.queue-artist');
            if (queueTitle && queueArtist) {
                queueTitle.textContent = title;
                queueArtist.textContent = artist;
                document.querySelector('.queue-item').classList.add('current');
            }
        }

        function handleSearch(query) {
            console.log('搜索:', query);
            // 这里可以添加实际的搜索逻辑
        }

        function searchFor(keyword) {
            const searchBox = document.querySelector('.search-box');
            if (searchBox) {
                searchBox.value = keyword;
                handleSearch(keyword);
            }
        }

        // 播放控制功能
        function togglePlay() {
            const playBtn = document.querySelector('.control-btn.play');
            const isPlaying = playBtn.textContent === '⏸';
            playBtn.textContent = isPlaying ? '▶' : '⏸';

            // 控制黑胶动画
            const vinylAnimation = document.querySelector('.vinyl-animation');
            if (vinylAnimation) {
                vinylAnimation.style.animationPlayState = isPlaying ? 'paused' : 'running';
            }
        }

        function toggleFavorite() {
            const favoriteBtn = document.querySelector('.control-btn.favorite');
            favoriteBtn.classList.toggle('active');
        }

        // 模拟进度条更新
        function updateProgress() {
            const progressFill = document.querySelector('.song-progress-fill');
            const timeDisplay = document.querySelector('.song-time span:first-child');

            let currentTime = 83; // 1:23 in seconds
            const totalTime = 252; // 4:12 in seconds

            setInterval(() => {
                currentTime += 1;
                if (currentTime > totalTime) {
                    currentTime = 0;
                }

                const percentage = (currentTime / totalTime) * 100;
                progressFill.style.width = percentage + '%';

                const minutes = Math.floor(currentTime / 60);
                const seconds = currentTime % 60;
                timeDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加播放按钮事件监听
            document.querySelector('.control-btn.play').addEventListener('click', togglePlay);
            document.querySelector('.control-btn.favorite').addEventListener('click', toggleFavorite);

            // 启动进度条更新
            updateProgress();

            // 添加队列项点击事件
            document.querySelectorAll('.queue-item').forEach(item => {
                item.addEventListener('click', function() {
                    const title = this.querySelector('.queue-title-text').textContent;
                    const artist = this.querySelector('.queue-artist').textContent;
                    playMusic(title, artist);
                });
            });
        });
    </script>
</body>
</html>
