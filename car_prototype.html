<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>波尼音乐 - Android Automotive 车载版原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Roboto', sans-serif;
            background: #303030;
            color: #FFFFFF;
            overflow: hidden;
            height: 100vh;
            width: 100vw;
        }

        .car-screen {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
            background: linear-gradient(135deg, #303030 0%, #424242 50%, #303030 100%);
        }

        /* 顶部状态栏 */
        .status-bar {
            height: 40px;
            background: rgba(0, 0, 0, 0.9);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            font-size: 14px;
            color: #9E9E9E;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* 导航栏 */
        .nav-bar {
            height: 72px;
            background: #212121;
            display: flex;
            align-items: center;
            padding: 0 32px;
            border-bottom: 1px solid #424242;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .nav-item {
            padding: 12px 24px;
            margin: 0 8px;
            background: #424242;
            border-radius: 16dp;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            font-size: 16px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            justify-content: center;
        }

        .nav-item.active {
            background: #F44336;
            color: #FFFFFF;
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
            transform: translateY(-1px);
        }

        .nav-item:hover:not(.active) {
            background: #616161;
            transform: translateY(-1px);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            position: relative;
            background: #303030;
        }

        /* 侧边栏 */
        .sidebar {
            width: 280px;
            background: #424242;
            padding: 24px 16px;
            border-right: 1px solid #616161;
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: 32px;
        }

        .sidebar-title {
            font-size: 14px;
            font-weight: 600;
            color: #9E9E9E;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .sidebar-item {
            padding: 16px 20px;
            margin: 4px 0;
            background: transparent;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            font-size: 15px;
            display: flex;
            align-items: center;
            color: #FFFFFF;
            border: 1px solid transparent;
        }

        .sidebar-item:hover {
            background: #616161;
            border-color: #9E9E9E;
        }

        .sidebar-item.active {
            background: rgba(244, 67, 54, 0.2);
            border-color: #F44336;
            color: #F44336;
        }

        .sidebar-item .icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            background: currentColor;
            border-radius: 4px;
            opacity: 0.8;
        }

        /* 内容面板 */
        .content-panel {
            flex: 1;
            padding: 24px 32px;
            overflow-y: auto;
            background: #303030;
        }

        /* 播放界面 */
        .playing-screen {
            display: none;
            flex-direction: row;
            height: 100%;
            padding: 32px;
            gap: 32px;
            background: linear-gradient(135deg, #303030 0%, #424242 100%);
        }

        .playing-screen.active {
            display: flex;
        }

        .album-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            max-width: 500px;
        }

        .album-cover-container {
            position: relative;
            margin-bottom: 32px;
        }

        .album-cover {
            width: 320px;
            height: 320px;
            border-radius: 16px;
            background: linear-gradient(135deg, #F44336, #D32F2F);
            box-shadow: 0 16px 32px rgba(0, 0, 0, 0.4);
            position: relative;
            overflow: hidden;
            border: 4px solid #616161;
        }

        .album-cover::before {
            content: '♪';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 72px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: bold;
        }

        .vinyl-animation {
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 2px solid rgba(244, 67, 54, 0.3);
            border-radius: 50%;
            animation: rotate 8s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .song-info {
            text-align: center;
            margin-bottom: 32px;
            max-width: 400px;
        }

        .song-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #FFFFFF;
            line-height: 1.3;
        }

        .song-artist {
            font-size: 18px;
            color: #9E9E9E;
            margin-bottom: 16px;
        }

        .song-progress {
            width: 100%;
            height: 4px;
            background: #616161;
            border-radius: 2px;
            margin-bottom: 8px;
            overflow: hidden;
        }

        .song-progress-fill {
            height: 100%;
            background: #F44336;
            width: 35%;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .song-time {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
            color: #9E9E9E;
        }

        .controls {
            display: flex;
            align-items: center;
            gap: 24px;
            margin-top: 24px;
        }

        .control-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: #424242;
            border: 2px solid #616161;
            color: #FFFFFF;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn.play {
            width: 72px;
            height: 72px;
            background: #F44336;
            border-color: #F44336;
            font-size: 24px;
            box-shadow: 0 4px 16px rgba(244, 67, 54, 0.4);
        }

        .control-btn:hover {
            transform: scale(1.05);
            background: #616161;
        }

        .control-btn.play:hover {
            background: #D32F2F;
            box-shadow: 0 6px 20px rgba(244, 67, 54, 0.6);
        }

        .control-btn.favorite.active {
            color: #F44336;
            border-color: #F44336;
        }

        .lyrics-section {
            flex: 1;
            padding: 24px;
            background: #424242;
            border-radius: 16px;
            overflow-y: auto;
            border: 1px solid #616161;
            margin-left: 16px;
        }

        .lyrics-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #616161;
        }

        .lyrics-title {
            font-size: 18px;
            font-weight: 600;
            color: #FFFFFF;
        }

        .sound-wave {
            width: 32px;
            height: 20px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 20"><rect x="2" y="8" width="2" height="4" fill="%23F44336"><animate attributeName="height" values="4;12;4" dur="1s" repeatCount="indefinite"/><animate attributeName="y" values="8;4;8" dur="1s" repeatCount="indefinite"/></rect><rect x="8" y="6" width="2" height="8" fill="%23F44336"><animate attributeName="height" values="8;16;8" dur="1.2s" repeatCount="indefinite"/><animate attributeName="y" values="6;2;6" dur="1.2s" repeatCount="indefinite"/></rect><rect x="14" y="4" width="2" height="12" fill="%23F44336"><animate attributeName="height" values="12;20;12" dur="0.8s" repeatCount="indefinite"/><animate attributeName="y" values="4;0;4" dur="0.8s" repeatCount="indefinite"/></rect><rect x="20" y="6" width="2" height="8" fill="%23F44336"><animate attributeName="height" values="8;14;8" dur="1.1s" repeatCount="indefinite"/><animate attributeName="y" values="6;3;6" dur="1.1s" repeatCount="indefinite"/></rect><rect x="26" y="8" width="2" height="4" fill="%23F44336"><animate attributeName="height" values="4;10;4" dur="0.9s" repeatCount="indefinite"/><animate attributeName="y" values="8;5;8" dur="0.9s" repeatCount="indefinite"/></rect></svg>') no-repeat center;
            background-size: contain;
        }

        .lyric-line {
            padding: 12px 0;
            font-size: 16px;
            line-height: 1.6;
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            cursor: pointer;
            color: #9E9E9E;
            border-radius: 8px;
            margin: 4px 0;
        }

        .lyric-line.current {
            color: #F44336;
            font-weight: 600;
            font-size: 18px;
            background: rgba(244, 67, 54, 0.1);
            padding: 16px;
        }

        .lyric-line:hover {
            color: #FFFFFF;
            background: rgba(255, 255, 255, 0.1);
        }

        /* 音乐列表界面 */
        .content-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #424242;
        }

        .content-title {
            font-size: 24px;
            font-weight: 600;
            color: #FFFFFF;
        }

        .view-toggle {
            display: flex;
            background: #424242;
            border-radius: 8px;
            padding: 4px;
        }

        .view-toggle-btn {
            padding: 8px 16px;
            background: transparent;
            border: none;
            color: #9E9E9E;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .view-toggle-btn.active {
            background: #F44336;
            color: #FFFFFF;
        }

        .music-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 16px;
            padding: 16px 0;
        }

        .music-item {
            background: #424242;
            border-radius: 16px;
            padding: 16px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 16px;
            border: 1px solid transparent;
        }

        .music-item:hover {
            background: #616161;
            border-color: #9E9E9E;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .music-item-cover {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            background: linear-gradient(135deg, #F44336, #D32F2F);
            flex-shrink: 0;
            position: relative;
            overflow: hidden;
        }

        .music-item-cover::before {
            content: '♪';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: rgba(255, 255, 255, 0.8);
            font-size: 20px;
        }

        .music-item-info {
            flex: 1;
            min-width: 0;
        }

        .music-item-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #FFFFFF;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .music-item-artist {
            font-size: 14px;
            color: #9E9E9E;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .music-item-actions {
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .music-item:hover .music-item-actions {
            opacity: 1;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #616161;
            border: none;
            color: #FFFFFF;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .action-btn:hover {
            background: #F44336;
        }

        /* 搜索界面 */
        .search-section {
            margin-bottom: 24px;
        }

        .search-input {
            width: 100%;
            padding: 16px 20px;
            font-size: 16px;
            border: 2px solid #616161;
            border-radius: 16px;
            background: #424242;
            color: #FFFFFF;
            outline: none;
            transition: all 0.3s;
        }

        .search-input:focus {
            border-color: #F44336;
            box-shadow: 0 0 0 4px rgba(244, 67, 54, 0.2);
        }

        .search-input::placeholder {
            color: #9E9E9E;
        }

        .search-tag {
            padding: 8px 16px;
            background: #424242;
            border: 1px solid #616161;
            border-radius: 20px;
            color: #FFFFFF;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }

        .search-tag:hover {
            background: #F44336;
            border-color: #F44336;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 250px;
            }

            .album-cover {
                width: 280px;
                height: 280px;
            }
        }

        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="car-screen">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>🎵 波尼音乐</span>
                <span>•</span>
                <span>车载版</span>
            </div>
            <div class="status-right">
                <span>📶</span>
                <span>🔋 85%</span>
                <span>14:32</span>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-item active" onclick="showScreen('playing')">
                <span>🎵</span> 正在播放
            </div>
            <div class="nav-item" onclick="showScreen('local')">
                <span>📁</span> 本地音乐
            </div>
            <div class="nav-item" onclick="showScreen('online')">
                <span>🌐</span> 在线音乐
            </div>
            <div class="nav-item" onclick="showScreen('search')">
                <span>🔍</span> 搜索
            </div>
            <div class="nav-item" onclick="showScreen('settings')">
                <span>⚙️</span> 设置
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 播放界面 -->
            <div class="playing-screen active" id="playing-screen">
                <div class="album-section">
                    <div class="album-cover-container">
                        <div class="album-cover"></div>
                        <div class="vinyl-animation"></div>
                    </div>
                    <div class="song-info">
                        <div class="song-title">夜空中最亮的星</div>
                        <div class="song-artist">逃跑计划</div>
                        <div class="song-progress">
                            <div class="song-progress-fill"></div>
                        </div>
                        <div class="song-time">
                            <span>1:23</span>
                            <span>4:12</span>
                        </div>
                    </div>
                    <div class="controls">
                        <button class="control-btn" title="上一首">⏮</button>
                        <button class="control-btn play" title="播放/暂停">▶</button>
                        <button class="control-btn" title="下一首">⏭</button>
                        <button class="control-btn" title="播放模式">🔀</button>
                        <button class="control-btn favorite" title="收藏">❤</button>
                    </div>
                </div>
                <div class="lyrics-section">
                    <div class="lyrics-header">
                        <div class="lyrics-title">歌词</div>
                        <div class="sound-wave"></div>
                    </div>
                    <div class="lyric-line">夜空中最亮的星</div>
                    <div class="lyric-line current">能否听清</div>
                    <div class="lyric-line">那仰望的人</div>
                    <div class="lyric-line">心底的孤独和叹息</div>
                    <div class="lyric-line">夜空中最亮的星</div>
                    <div class="lyric-line">能否记起</div>
                    <div class="lyric-line">曾与我同行</div>
                    <div class="lyric-line">消失在风里的身影</div>
                    <div class="lyric-line">我祈祷拥有一颗透明的心灵</div>
                    <div class="lyric-line">和会流泪的眼睛</div>
                </div>
            </div>

            <!-- 其他界面内容将通过JavaScript动态加载 -->
            <div class="content-screen" id="other-screens" style="display: none;">
                <div class="sidebar">
                    <div class="sidebar-section">
                        <div class="sidebar-title">我的音乐</div>
                        <div class="sidebar-item active">
                            <div class="icon"></div>
                            我的歌单
                        </div>
                        <div class="sidebar-item">
                            <div class="icon"></div>
                            最近播放
                        </div>
                        <div class="sidebar-item">
                            <div class="icon"></div>
                            我喜欢的
                        </div>
                        <div class="sidebar-item">
                            <div class="icon"></div>
                            下载管理
                        </div>
                    </div>
                    <div class="sidebar-section">
                        <div class="sidebar-title">在线音乐</div>
                        <div class="sidebar-item">
                            <div class="icon"></div>
                            每日推荐
                        </div>
                        <div class="sidebar-item">
                            <div class="icon"></div>
                            歌单广场
                        </div>
                        <div class="sidebar-item">
                            <div class="icon"></div>
                            排行榜
                        </div>
                    </div>
                </div>
                <div class="content-panel" id="content-panel">
                    <!-- 动态内容 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        function showScreen(screenType) {
            // 更新导航栏状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // 显示对应界面
            const playingScreen = document.getElementById('playing-screen');
            const otherScreens = document.getElementById('other-screens');
            const contentPanel = document.getElementById('content-panel');

            if (screenType === 'playing') {
                playingScreen.style.display = 'flex';
                otherScreens.style.display = 'none';
            } else {
                playingScreen.style.display = 'none';
                otherScreens.style.display = 'flex';

                // 根据类型加载不同内容
                loadContent(screenType, contentPanel);
            }
        }

        function loadContent(type, container) {
            let content = '';

            switch(type) {
                case 'local':
                    content = generateMusicList('本地音乐', [
                        {title: '夜空中最亮的星', artist: '逃跑计划'},
                        {title: '成都', artist: '赵雷'},
                        {title: '南山南', artist: '马頔'},
                        {title: '理想', artist: '赵雷'},
                        {title: '消愁', artist: '毛不易'},
                        {title: '像我这样的人', artist: '毛不易'}
                    ]);
                    break;
                case 'online':
                    content = generateMusicList('在线音乐', [
                        {title: '稻香', artist: '周杰伦'},
                        {title: '青花瓷', artist: '周杰伦'},
                        {title: '告白气球', artist: '周杰伦'},
                        {title: '演员', artist: '薛之谦'},
                        {title: '刚好遇见你', artist: '李玉刚'},
                        {title: '体面', artist: '于文文'}
                    ]);
                    break;
                case 'search':
                    content = `
                        <div class="content-header">
                            <div class="content-title">搜索音乐</div>
                        </div>
                        <div class="search-section">
                            <input type="text" class="search-input" placeholder="🔍 搜索歌曲、歌手、专辑..." oninput="handleSearch(this.value)">
                        </div>
                        <div class="search-suggestions" style="margin-bottom: 24px;">
                            <div style="color: #9E9E9E; margin-bottom: 16px; font-size: 14px;">热门搜索</div>
                            <div style="display: flex; flex-wrap: wrap; gap: 12px;">
                                <span class="search-tag" onclick="searchFor('周杰伦')">周杰伦</span>
                                <span class="search-tag" onclick="searchFor('夜空中最亮的星')">夜空中最亮的星</span>
                                <span class="search-tag" onclick="searchFor('成都')">成都</span>
                                <span class="search-tag" onclick="searchFor('演员')">演员</span>
                            </div>
                        </div>
                        ${generateMusicList('搜索结果', [
                            {title: '夜空中最亮的星', artist: '逃跑计划'},
                            {title: '成都', artist: '赵雷'},
                            {title: '演员', artist: '薛之谦'},
                            {title: '告白气球', artist: '周杰伦'}
                        ])}
                    `;
                    break;
                case 'settings':
                    content = `
                        <h2 style="margin-bottom: 30px; color: #007AFF;">设置</h2>
                        <div class="music-item">
                            <div class="music-item-info">
                                <div class="music-item-title">音质设置</div>
                                <div class="music-item-artist">高品质 320kbps</div>
                            </div>
                        </div>
                        <div class="music-item">
                            <div class="music-item-info">
                                <div class="music-item-title">夜间模式</div>
                                <div class="music-item-artist">已开启</div>
                            </div>
                        </div>
                        <div class="music-item">
                            <div class="music-item-info">
                                <div class="music-item-title">API域名设置</div>
                                <div class="music-item-artist">localhost:3000</div>
                            </div>
                        </div>
                    `;
                    break;
            }

            container.innerHTML = content;
        }

        function generateMusicList(title, songs) {
            return `
                <div class="content-header">
                    <div class="content-title">${title}</div>
                    <div class="view-toggle">
                        <button class="view-toggle-btn active">网格</button>
                        <button class="view-toggle-btn">列表</button>
                    </div>
                </div>
                <div class="music-list">
                    ${songs.map(song => `
                        <div class="music-item" onclick="playMusic('${song.title}', '${song.artist}')">
                            <div class="music-item-cover"></div>
                            <div class="music-item-info">
                                <div class="music-item-title">${song.title}</div>
                                <div class="music-item-artist">${song.artist}</div>
                            </div>
                            <div class="music-item-actions">
                                <button class="action-btn" title="播放">▶</button>
                                <button class="action-btn" title="收藏">❤</button>
                                <button class="action-btn" title="更多">⋯</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function playMusic(title, artist) {
            // 切换到播放界面
            showScreen('playing');

            // 更新播放信息
            document.querySelector('.song-title').textContent = title;
            document.querySelector('.song-artist').textContent = artist;

            // 更新导航栏
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector('.nav-item').classList.add('active');
        }

        function handleSearch(query) {
            console.log('搜索:', query);
            // 这里可以添加实际的搜索逻辑
        }

        function searchFor(keyword) {
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.value = keyword;
                handleSearch(keyword);
            }
        }

        // 播放控制功能
        function togglePlay() {
            const playBtn = document.querySelector('.control-btn.play');
            const isPlaying = playBtn.textContent === '⏸';
            playBtn.textContent = isPlaying ? '▶' : '⏸';

            // 控制黑胶动画
            const vinylAnimation = document.querySelector('.vinyl-animation');
            if (vinylAnimation) {
                vinylAnimation.style.animationPlayState = isPlaying ? 'paused' : 'running';
            }
        }

        function toggleFavorite() {
            const favoriteBtn = document.querySelector('.control-btn.favorite');
            favoriteBtn.classList.toggle('active');
        }

        // 歌词滚动功能
        function scrollToCurrentLyric() {
            const currentLyric = document.querySelector('.lyric-line.current');
            if (currentLyric) {
                currentLyric.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        // 模拟歌词进度
        function simulateLyricProgress() {
            const lyrics = document.querySelectorAll('.lyric-line');
            let currentIndex = 1; // 从第二行开始（索引1）

            setInterval(() => {
                lyrics.forEach(lyric => lyric.classList.remove('current'));
                if (currentIndex < lyrics.length) {
                    lyrics[currentIndex].classList.add('current');
                    scrollToCurrentLyric();
                    currentIndex++;
                } else {
                    currentIndex = 0; // 重新开始
                }
            }, 3000); // 每3秒切换一行歌词
        }

        // 初始化加载本地音乐内容
        document.addEventListener('DOMContentLoaded', function() {
            const contentPanel = document.getElementById('content-panel');
            loadContent('local', contentPanel);

            // 添加播放按钮事件监听
            document.querySelector('.control-btn.play').addEventListener('click', togglePlay);
            document.querySelector('.control-btn.favorite').addEventListener('click', toggleFavorite);

            // 启动歌词滚动模拟
            setTimeout(simulateLyricProgress, 2000);
        });
    </script>
</body>
</html>
