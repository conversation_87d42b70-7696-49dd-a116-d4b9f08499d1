<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>波尼音乐 - Android Automotive 车载版原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            overflow: hidden;
            height: 100vh;
            width: 100vw;
        }

        .car-screen {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 导航栏 */
        .nav-bar {
            height: 80px;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            padding: 0 40px;
            border-bottom: 2px solid #333;
        }

        .nav-item {
            padding: 15px 30px;
            margin: 0 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 18px;
            font-weight: bold;
        }

        .nav-item.active {
            background: #007AFF;
            box-shadow: 0 4px 15px rgba(0, 122, 255, 0.4);
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            position: relative;
        }

        /* 侧边栏 */
        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.6);
            padding: 30px 20px;
            border-right: 2px solid #333;
        }

        .sidebar-item {
            padding: 20px;
            margin: 10px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 16px;
            display: flex;
            align-items: center;
        }

        .sidebar-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(5px);
        }

        .sidebar-item .icon {
            width: 24px;
            height: 24px;
            margin-right: 15px;
            background: #007AFF;
            border-radius: 50%;
        }

        /* 内容面板 */
        .content-panel {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        /* 播放界面 */
        .playing-screen {
            display: none;
            flex-direction: row;
            height: 100%;
            padding: 40px;
            gap: 40px;
        }

        .playing-screen.active {
            display: flex;
        }

        .album-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .album-cover {
            width: 350px;
            height: 350px;
            border-radius: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
        }

        .album-cover::before {
            content: '♪';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 80px;
            color: rgba(255, 255, 255, 0.8);
        }

        .song-info {
            text-align: center;
            margin-bottom: 30px;
        }

        .song-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .song-artist {
            font-size: 20px;
            color: rgba(255, 255, 255, 0.7);
        }

        .controls {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .control-btn {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-btn.play {
            width: 90px;
            height: 90px;
            background: #007AFF;
            font-size: 30px;
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.4);
        }

        .lyrics-section {
            flex: 1;
            padding: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            overflow-y: auto;
        }

        .lyrics-title {
            font-size: 24px;
            margin-bottom: 20px;
            text-align: center;
            color: #007AFF;
        }

        .lyric-line {
            padding: 15px 0;
            font-size: 18px;
            line-height: 1.6;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }

        .lyric-line.current {
            color: #007AFF;
            font-weight: bold;
            font-size: 20px;
            transform: scale(1.05);
        }

        .lyric-line:hover {
            color: #007AFF;
        }

        /* 音乐列表界面 */
        .music-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px 0;
        }

        .music-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .music-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
        }

        .music-item-cover {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            flex-shrink: 0;
        }

        .music-item-info {
            flex: 1;
        }

        .music-item-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .music-item-artist {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
        }

        /* 搜索界面 */
        .search-section {
            margin-bottom: 30px;
        }

        .search-input {
            width: 100%;
            padding: 20px;
            font-size: 18px;
            border: none;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            outline: none;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 250px;
            }
            
            .album-cover {
                width: 280px;
                height: 280px;
            }
        }

        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="car-screen">
        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-item active" onclick="showScreen('playing')">正在播放</div>
            <div class="nav-item" onclick="showScreen('local')">本地音乐</div>
            <div class="nav-item" onclick="showScreen('online')">在线音乐</div>
            <div class="nav-item" onclick="showScreen('search')">搜索</div>
            <div class="nav-item" onclick="showScreen('settings')">设置</div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 播放界面 -->
            <div class="playing-screen active" id="playing-screen">
                <div class="album-section">
                    <div class="album-cover"></div>
                    <div class="song-info">
                        <div class="song-title">夜空中最亮的星</div>
                        <div class="song-artist">逃跑计划</div>
                    </div>
                    <div class="controls">
                        <button class="control-btn">⏮</button>
                        <button class="control-btn play">▶</button>
                        <button class="control-btn">⏭</button>
                        <button class="control-btn">🔀</button>
                        <button class="control-btn">❤</button>
                    </div>
                </div>
                <div class="lyrics-section">
                    <div class="lyrics-title">歌词</div>
                    <div class="lyric-line">夜空中最亮的星</div>
                    <div class="lyric-line current">能否听清</div>
                    <div class="lyric-line">那仰望的人</div>
                    <div class="lyric-line">心底的孤独和叹息</div>
                    <div class="lyric-line">夜空中最亮的星</div>
                    <div class="lyric-line">能否记起</div>
                    <div class="lyric-line">曾与我同行</div>
                    <div class="lyric-line">消失在风里的身影</div>
                </div>
            </div>

            <!-- 其他界面内容将通过JavaScript动态加载 -->
            <div class="content-screen" id="other-screens" style="display: none;">
                <div class="sidebar">
                    <div class="sidebar-item">
                        <div class="icon"></div>
                        我的歌单
                    </div>
                    <div class="sidebar-item">
                        <div class="icon"></div>
                        最近播放
                    </div>
                    <div class="sidebar-item">
                        <div class="icon"></div>
                        我喜欢的
                    </div>
                    <div class="sidebar-item">
                        <div class="icon"></div>
                        下载管理
                    </div>
                </div>
                <div class="content-panel" id="content-panel">
                    <!-- 动态内容 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        function showScreen(screenType) {
            // 更新导航栏状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');

            // 显示对应界面
            const playingScreen = document.getElementById('playing-screen');
            const otherScreens = document.getElementById('other-screens');
            const contentPanel = document.getElementById('content-panel');

            if (screenType === 'playing') {
                playingScreen.style.display = 'flex';
                otherScreens.style.display = 'none';
            } else {
                playingScreen.style.display = 'none';
                otherScreens.style.display = 'flex';
                
                // 根据类型加载不同内容
                loadContent(screenType, contentPanel);
            }
        }

        function loadContent(type, container) {
            let content = '';
            
            switch(type) {
                case 'local':
                    content = generateMusicList('本地音乐', [
                        {title: '夜空中最亮的星', artist: '逃跑计划'},
                        {title: '成都', artist: '赵雷'},
                        {title: '南山南', artist: '马頔'},
                        {title: '理想', artist: '赵雷'},
                        {title: '消愁', artist: '毛不易'},
                        {title: '像我这样的人', artist: '毛不易'}
                    ]);
                    break;
                case 'online':
                    content = generateMusicList('在线音乐', [
                        {title: '稻香', artist: '周杰伦'},
                        {title: '青花瓷', artist: '周杰伦'},
                        {title: '告白气球', artist: '周杰伦'},
                        {title: '演员', artist: '薛之谦'},
                        {title: '刚好遇见你', artist: '李玉刚'},
                        {title: '体面', artist: '于文文'}
                    ]);
                    break;
                case 'search':
                    content = `
                        <div class="search-section">
                            <input type="text" class="search-input" placeholder="搜索歌曲、歌手、专辑...">
                        </div>
                        ${generateMusicList('搜索结果', [
                            {title: '搜索示例歌曲1', artist: '示例歌手1'},
                            {title: '搜索示例歌曲2', artist: '示例歌手2'}
                        ])}
                    `;
                    break;
                case 'settings':
                    content = `
                        <h2 style="margin-bottom: 30px; color: #007AFF;">设置</h2>
                        <div class="music-item">
                            <div class="music-item-info">
                                <div class="music-item-title">音质设置</div>
                                <div class="music-item-artist">高品质 320kbps</div>
                            </div>
                        </div>
                        <div class="music-item">
                            <div class="music-item-info">
                                <div class="music-item-title">夜间模式</div>
                                <div class="music-item-artist">已开启</div>
                            </div>
                        </div>
                        <div class="music-item">
                            <div class="music-item-info">
                                <div class="music-item-title">API域名设置</div>
                                <div class="music-item-artist">localhost:3000</div>
                            </div>
                        </div>
                    `;
                    break;
            }
            
            container.innerHTML = content;
        }

        function generateMusicList(title, songs) {
            return `
                <h2 style="margin-bottom: 30px; color: #007AFF;">${title}</h2>
                <div class="music-list">
                    ${songs.map(song => `
                        <div class="music-item" onclick="playMusic('${song.title}', '${song.artist}')">
                            <div class="music-item-cover"></div>
                            <div class="music-item-info">
                                <div class="music-item-title">${song.title}</div>
                                <div class="music-item-artist">${song.artist}</div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        function playMusic(title, artist) {
            // 切换到播放界面
            showScreen('playing');
            
            // 更新播放信息
            document.querySelector('.song-title').textContent = title;
            document.querySelector('.song-artist').textContent = artist;
            
            // 更新导航栏
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector('.nav-item').classList.add('active');
        }

        // 初始化加载本地音乐内容
        document.addEventListener('DOMContentLoaded', function() {
            const contentPanel = document.getElementById('content-panel');
            loadContent('local', contentPanel);
        });
    </script>
</body>
</html>
