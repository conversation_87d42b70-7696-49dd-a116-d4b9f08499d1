{"logs": [{"outputFile": "me.wcy.music.app-mergeDebugResources-66:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\31112261c7df96d69093b9be7b3f632a\\transformed\\jetified-media3-session-1.4.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,122,201,261,340,417,485,553,616,688,754,807,884,951,1028,1093,1169,1233,1302,1367,1431,1500,1572,1647", "endColumns": "66,78,59,78,76,67,67,62,71,65,52,76,66,76,64,75,63,68,64,63,68,71,74,78", "endOffsets": "117,196,256,335,412,480,548,611,683,749,802,879,946,1023,1088,1164,1228,1297,1362,1426,1495,1567,1642,1721"}, "to": {"startLines": "66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4739,4919,4998,5058,5137,5214,5282,5350,5413,5485,5551,5604,5681,5748,5825,5890,5966,6030,10397,10462,10526,10595,10667,10742", "endColumns": "66,78,59,78,76,67,67,62,71,65,52,76,66,76,64,75,63,68,64,63,68,71,74,78", "endOffsets": "4801,4993,5053,5132,5209,5277,5345,5408,5480,5546,5599,5676,5743,5820,5885,5961,6025,6094,10457,10521,10590,10662,10737,10816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\286503f715fac5b6668bc7afcac59c2c\\transformed\\jetified-media3-ui-1.4.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,602,672,741,811,887,962,1017,1078,1152,1226,1288,1349,1408,1473,1562,1648,1737,1800,1867,1932,1987,2061,2134,2195,2258,2310,2368,2415,2476,2532,2594,2651,2711,2767,2822,2885,2934,2987,3054,3121,3170", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,48,52,66,66,48,60", "endOffsets": "276,439,597,667,736,806,882,957,1012,1073,1147,1221,1283,1344,1403,1468,1557,1643,1732,1795,1862,1927,1982,2056,2129,2190,2253,2305,2363,2410,2471,2527,2589,2646,2706,2762,2817,2880,2929,2982,3049,3116,3165,3226"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,376,539,6099,6169,6238,6308,6384,6459,6514,6575,6649,6723,6785,6846,6905,6970,7059,7145,7234,7297,7364,7429,7484,7558,7631,7692,8313,8365,8423,8470,8531,8587,8649,8706,8766,8822,8877,8940,8989,9042,9109,9176,9225", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,48,52,66,66,48,60", "endOffsets": "371,534,692,6164,6233,6303,6379,6454,6509,6570,6644,6718,6780,6841,6900,6965,7054,7140,7229,7292,7359,7424,7479,7553,7626,7687,7750,8360,8418,8465,8526,8582,8644,8701,8761,8817,8872,8935,8984,9037,9104,9171,9220,9281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8110c868e8d28496c81ffd9e11046d18\\transformed\\appcompat-1.7.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "844,939,1032,1132,1214,1311,1419,1496,1571,1663,1757,1848,1944,2039,2133,2229,2321,2413,2505,2583,2679,2774,2869,2966,3062,3160,3311,14289", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "934,1027,1127,1209,1306,1414,1491,1566,1658,1752,1843,1939,2034,2128,2224,2316,2408,2500,2578,2674,2769,2864,2961,3057,3155,3306,3400,14363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\40b0160c565c3675e15a0634390e90ad\\transformed\\preference-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,441,609,688", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "166,247,317,436,604,683,759"}, "to": {"startLines": "65,136,204,206,212,213,214", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4673,9286,14031,14170,14687,14855,14934", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "4734,9362,14096,14284,14850,14929,15005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9151924b14ac859a0a182a0b1a1df226\\transformed\\core-1.13.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "55,56,57,58,59,60,61,211", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3742,3834,3933,4027,4121,4214,4307,14586", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3829,3928,4022,4116,4209,4302,4398,14682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\be9ab8b566fc36ab0e145ee170d21c48\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,478,554", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "106,162,220,273,345,399,473,549,608"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7755,7811,7867,7925,7978,8050,8104,8178,8254", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "7806,7862,7920,7973,8045,8099,8173,8249,8308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ed9553fb9af75b90a85668bb0aac2cdb\\transformed\\material-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,310,371,438,507,584,674,781,854,905,967,1045,1104,1162,1240,1301,1358,1414,1473,1531,1585,1671,1727,1785,1839,1904,1997,2071,2143,2223,2297,2375,2495,2558,2621,2720,2797,2871,2921,2972,3038,3102,3170,3241,3313,3374,3445,3512,3572,3660,3740,3803,3886,3971,4045,4110,4186,4234,4308,4372,4448,4526,4588,4652,4715,4781,4861,4941,5017,5098,5152,5207,5276,5351,5424", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "242,305,366,433,502,579,669,776,849,900,962,1040,1099,1157,1235,1296,1353,1409,1468,1526,1580,1666,1722,1780,1834,1899,1992,2066,2138,2218,2292,2370,2490,2553,2616,2715,2792,2866,2916,2967,3033,3097,3165,3236,3308,3369,3440,3507,3567,3655,3735,3798,3881,3966,4040,4105,4181,4229,4303,4367,4443,4521,4583,4647,4710,4776,4856,4936,5012,5093,5147,5202,5271,5346,5419,5489"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,67,68,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,205,208,209,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "697,3405,3468,3529,3596,3665,4403,4493,4600,4806,4857,9367,9445,9504,9562,9640,9701,9758,9814,9873,9931,9985,10071,10127,10185,10239,10304,10821,10895,10967,11047,11121,11199,11319,11382,11445,11544,11621,11695,11745,11796,11862,11926,11994,12065,12137,12198,12269,12336,12396,12484,12564,12627,12710,12795,12869,12934,13010,13058,13132,13196,13272,13350,13412,13476,13539,13605,13685,13765,13841,13922,13976,14101,14368,14443,14516", "endLines": "22,50,51,52,53,54,62,63,64,67,68,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,205,208,209,210", "endColumns": "12,62,60,66,68,76,89,106,72,50,61,77,58,57,77,60,56,55,58,57,53,85,55,57,53,64,92,73,71,79,73,77,119,62,62,98,76,73,49,50,65,63,67,70,71,60,70,66,59,87,79,62,82,84,73,64,75,47,73,63,75,77,61,63,62,65,79,79,75,80,53,54,68,74,72,69", "endOffsets": "839,3463,3524,3591,3660,3737,4488,4595,4668,4852,4914,9440,9499,9557,9635,9696,9753,9809,9868,9926,9980,10066,10122,10180,10234,10299,10392,10890,10962,11042,11116,11194,11314,11377,11440,11539,11616,11690,11740,11791,11857,11921,11989,12060,12132,12193,12264,12331,12391,12479,12559,12622,12705,12790,12864,12929,13005,13053,13127,13191,13267,13345,13407,13471,13534,13600,13680,13760,13836,13917,13971,14026,14165,14438,14511,14581"}}]}]}