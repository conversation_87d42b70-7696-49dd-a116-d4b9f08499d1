<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <top.wangchenyan.common.widget.TitleLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tlShowBack="false"
        app:tlTitleContentLayout="@layout/title_discover"
        app:tlTitleText="" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingBottom="16dp">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="128dp"
                android:layout_marginTop="8dp">

                <com.youth.banner.Banner
                    android:id="@+id/banner"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:banner_auto_loop="true"
                    app:banner_infinite_loop="true"
                    app:banner_loop_time="5000" />

                <com.hjq.shape.view.ShapeTextView
                    android:id="@+id/bannerPlaceholder"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="16dp"
                    android:gravity="center"
                    android:text="广告位招租~"
                    app:shape_radius="12dp"
                    app:shape_solidColor="@color/common_divider" />
            </FrameLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btnRecommendSong"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawableTop="@drawable/ic_recommend"
                    android:drawablePadding="2dp"
                    android:drawableTint="@color/common_theme_color"
                    android:gravity="center_horizontal"
                    android:text="每日推荐"
                    android:textColor="@color/common_text_h1_color"
                    android:textSize="12dp" />

                <TextView
                    android:id="@+id/btnPrivateFm"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawableTop="@drawable/ic_radio"
                    android:drawablePadding="2dp"
                    android:drawableTint="@color/common_theme_color"
                    android:gravity="center_horizontal"
                    android:text="私人漫游"
                    android:textColor="@color/common_text_h1_color"
                    android:textSize="12dp" />

                <TextView
                    android:id="@+id/btnRecommendPlaylist"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawableTop="@drawable/ic_discovery_playlist"
                    android:drawablePadding="2dp"
                    android:drawableTint="@color/common_theme_color"
                    android:gravity="center_horizontal"
                    android:text="歌单"
                    android:textColor="@color/common_text_h1_color"
                    android:textSize="12dp" />

                <TextView
                    android:id="@+id/btnRank"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawableTop="@drawable/ic_discovery_rank"
                    android:drawablePadding="2dp"
                    android:drawableTint="@color/common_theme_color"
                    android:gravity="center_horizontal"
                    android:text="排行榜"
                    android:textColor="@color/common_text_h1_color"
                    android:textSize="12dp" />
            </LinearLayout>

            <TextView
                android:id="@+id/tvRecommendPlaylist"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:drawableEnd="@drawable/ic_arrow_right"
                android:drawableTint="@color/common_text_h1_color"
                android:text="推荐歌单"
                android:textColor="@color/common_text_h1_color"
                android:textSize="17dp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvRecommendPlaylist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:clipToPadding="false"
                android:paddingHorizontal="16dp"
                tools:itemCount="1"
                tools:listitem="@layout/item_discover_playlist" />

            <TextView
                android:id="@+id/tvRankingList"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="16dp"
                android:drawableEnd="@drawable/ic_arrow_right"
                android:drawableTint="@color/common_text_h1_color"
                android:text="排行榜"
                android:textColor="@color/common_text_h1_color"
                android:textSize="17dp"
                android:textStyle="bold" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vpRankingList"
                android:layout_width="match_parent"
                android:layout_height="224dp"
                android:layout_marginTop="12dp" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>