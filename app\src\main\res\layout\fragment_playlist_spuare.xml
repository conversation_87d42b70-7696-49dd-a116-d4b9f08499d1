<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <top.wangchenyan.common.widget.TitleLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tlTitleText="歌单广场" />

    <LinearLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            style="@style/TabLayout"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            app:tabMode="scrollable" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPage2"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>

    <me.wcy.music.widget.PlayBar
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>