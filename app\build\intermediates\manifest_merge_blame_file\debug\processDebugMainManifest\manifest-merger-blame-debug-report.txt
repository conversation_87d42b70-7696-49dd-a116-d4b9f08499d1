1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="me.wcy.music"
4    android:versionCode="2030001"
5    android:versionName="2.3.0-beta01" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="36" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:5:5-79
12-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:5:22-76
13    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
13-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:6:5-76
13-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:6:22-73
14    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
14-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:7:5-81
14-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:7:22-78
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:8:5-77
15-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:8:22-74
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
16-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:9:5-92
16-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:9:22-89
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:8:5-66
17-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:8:22-63
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:9:5-80
18-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:9:22-77
19    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
19-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:11:5-76
19-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:11:22-73
20    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
20-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:12:5-75
20-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:12:22-72
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
21-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:13:5-75
21-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:13:22-72
22    <uses-permission android:name="android.permission.CAMERA" />
22-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:14:5-65
22-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:14:22-62
23
24    <queries>
24-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:16:5-20:15
25        <intent>
25-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:17:9-19:18
26            <action android:name="android.intent.action.SEND" />
26-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:18:13-65
26-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:18:21-62
27        </intent>
28        <intent>
28-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:10:9-12:18
29            <action android:name="android.intent.action.MAIN" />
29-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:43:17-69
29-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:43:25-66
30        </intent>
31        <intent>
31-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:13:9-15:18
32            <action android:name="android.intent.action.VIEW" />
32-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:14:13-65
32-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:14:21-62
33        </intent>
34    </queries>
35
36    <uses-feature android:name="android.hardware.camera" />
36-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:13:5-60
36-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:13:19-57
37    <uses-feature android:name="android.hardware.camera.autofocus" />
37-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:14:5-70
37-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:14:19-67
38
39    <uses-permission android:name="android.permission.FLASHLIGHT" />
39-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:16:5-69
39-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:16:22-66
40    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
40-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:20:5-84
40-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:20:22-81
41
42    <permission
42-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
43        android:name="me.wcy.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
43-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
44        android:protectionLevel="signature" />
44-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
45
46    <uses-permission android:name="me.wcy.music.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
47
48    <application
48-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:11:5-59:19
49        android:name="me.wcy.music.MusicApplication"
49-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:12:9-41
50        android:allowBackup="true"
50-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:13:9-35
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\9151924b14ac859a0a182a0b1a1df226\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
52        android:debuggable="true"
53        android:extractNativeLibs="true"
54        android:icon="@drawable/ic_launcher"
54-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:14:9-45
55        android:label="@string/app_name"
55-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:15:9-41
56        android:networkSecurityConfig="@xml/network_security_config"
56-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:16:9-69
57        android:roundIcon="@drawable/ic_launcher_round"
57-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:17:9-56
58        android:supportsRtl="true"
58-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:26:9-35
59        android:theme="@style/AppTheme" >
59-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:18:9-40
60        <service
60-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:20:9-27:19
61            android:name="me.wcy.music.service.MusicService"
61-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:21:13-49
62            android:exported="true"
62-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:22:13-36
63            android:foregroundServiceType="mediaPlayback" >
63-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:23:13-58
64            <intent-filter>
64-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:24:13-26:29
65                <action android:name="androidx.media3.session.MediaSessionService" />
65-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:25:17-86
65-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:25:25-83
66            </intent-filter>
67        </service>
68
69        <receiver
69-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:29:9-35:20
70            android:name="me.wcy.music.download.DownloadReceiver"
70-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:30:13-54
71            android:exported="true" >
71-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:31:13-36
72            <intent-filter>
72-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:32:13-34:29
73                <action android:name="android.intent.action.DOWNLOAD_COMPLETE" />
73-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:33:17-82
73-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:33:25-79
74            </intent-filter>
75        </receiver>
76
77        <activity
77-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:37:9-47:20
78            android:name="me.wcy.music.main.MainActivity"
78-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:38:13-46
79            android:exported="true"
79-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:39:13-36
80            android:label="@string/app_name"
80-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:40:13-45
81            android:launchMode="singleTop" >
81-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:41:13-43
82            <intent-filter>
82-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:42:13-46:29
83                <action android:name="android.intent.action.MAIN" />
83-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:43:17-69
83-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:43:25-66
84
85                <category android:name="android.intent.category.LAUNCHER" />
85-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:45:17-77
85-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:45:27-74
86            </intent-filter>
87        </activity>
88        <activity android:name="me.wcy.music.common.MusicFragmentContainerActivity" />
88-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:48:9-75
88-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:48:19-72
89        <activity
89-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:49:9-51:52
90            android:name="me.wcy.music.main.SettingsActivity"
90-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:50:13-50
91            android:label="@string/menu_setting" />
91-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:51:13-49
92        <activity
92-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:52:9-54:50
93            android:name="me.wcy.music.main.AboutActivity"
93-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:53:13-47
94            android:label="@string/menu_about" />
94-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:54:13-47
95        <activity
95-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:55:9-58:53
96            android:name="me.wcy.music.main.playing.PlayingActivity"
96-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:56:13-69
97            android:launchMode="singleTop"
97-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:57:13-43
98            android:theme="@style/AppTheme.Popup" />
98-->C:\Users\<USER>\Desktop\ponymusic-master\app\src\main\AndroidManifest.xml:58:13-50
99        <activity
99-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:23:9-25:50
100            android:name="top.wangchenyan.common.ui.activity.FragmentContainerActivity"
100-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:24:13-88
101            android:screenOrientation="behind" />
101-->[com.github.wangchenyan:android-common:1.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\03c19d3e6f54fd9df39daa5089142c13\transformed\jetified-android-common-1.0.2\AndroidManifest.xml:25:13-47
102
103        <meta-data
103-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:11:9-13:36
104            android:name="android.notch_support"
104-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:12:13-49
105            android:value="true" /> <!-- PermissonActivity -->
105-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:13:13-33
106        <activity
106-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:15:9-17:75
107            android:name="com.lxj.xpopup.util.XPermission$PermissionActivity"
107-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:16:13-78
108            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
108-->[com.github.li-xiaojun:XPopup:2.7.9] C:\Users\<USER>\.gradle\caches\transforms-4\3597477602625afeffa5d8efbcacac71\transformed\jetified-XPopup-2.7.9\AndroidManifest.xml:17:13-72
109
110        <provider
110-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:27:9-35:20
111            android:name="cn.bertsir.zbar.utils.QrFileProvider"
111-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:28:13-64
112            android:authorities="me.wcy.music.zbar.FileProvider"
112-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:29:13-69
113            android:exported="false"
113-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:30:13-37
114            android:grantUriPermissions="true" >
114-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:31:13-47
115            <meta-data
115-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:32:13-34:57
116                android:name="android.support.FILE_PROVIDER_PATHS"
116-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:33:17-67
117                android:resource="@xml/qr_file_paths" />
117-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:34:17-54
118        </provider>
119
120        <activity
120-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:37:9-39:77
121            android:name="cn.bertsir.zbar.QRActivity"
121-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:38:13-54
122            android:configChanges="keyboardHidden|orientation|screenSize" />
122-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:39:13-74
123        <activity
123-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:40:9-42:58
124            android:name="cn.bertsir.zbar.utils.PermissionUtils$PermissionActivity"
124-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:41:13-84
125            android:theme="@style/ActivityTranslucent" />
125-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:42:13-55
126        <activity android:name="com.soundcloud.android.crop.CropImageActivity" />
126-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:43:9-82
126-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:43:19-79
127        <activity
127-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:19:9-24:75
128            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
128-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:20:13-83
129            android:configChanges="orientation|keyboardHidden|screenSize"
129-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:21:13-74
130            android:exported="false"
130-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:22:13-37
131            android:theme="@style/ActivityTranslucent"
131-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:23:13-55
132            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
132-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:24:13-72
133        <activity
133-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:25:9-31:75
134            android:name="com.blankj.utilcode.util.UtilsTransActivity"
134-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:26:13-71
135            android:configChanges="orientation|keyboardHidden|screenSize"
135-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:27:13-74
136            android:exported="false"
136-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:28:13-37
137            android:multiprocess="true"
137-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:29:13-40
138            android:theme="@style/ActivityTranslucent"
138-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:30:13-55
139            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
139-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:31:13-72
140
141        <provider
141-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:33:9-41:20
142            android:name="com.blankj.utilcode.util.UtilsFileProvider"
142-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:34:13-70
143            android:authorities="me.wcy.music.utilcode.fileprovider"
143-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:35:13-73
144            android:exported="false"
144-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:36:13-37
145            android:grantUriPermissions="true" >
145-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:37:13-47
146            <meta-data
146-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:32:13-34:57
147                android:name="android.support.FILE_PROVIDER_PATHS"
147-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:33:17-67
148                android:resource="@xml/util_code_provider_paths" />
148-->[cn.bertsir.zbarLibary:zbarlibary:1.4.2] C:\Users\<USER>\.gradle\caches\transforms-4\ed10cb5ec4666c40869ce00b9160eb62\transformed\jetified-zbarlibary-1.4.2\AndroidManifest.xml:34:17-54
149        </provider>
150
151        <service
151-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:43:9-49:19
152            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
152-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:44:13-81
153            android:exported="false" >
153-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:45:13-37
154            <intent-filter>
154-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
155                <action android:name="me.wcy.music.messenger" />
155-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:17-69
155-->[com.blankj:utilcodex:1.31.1] C:\Users\<USER>\.gradle\caches\transforms-4\148ae947ff43ca6b9b3284c945ceda29\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:25-66
156            </intent-filter>
157        </service>
158        <service
158-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
159            android:name="androidx.room.MultiInstanceInvalidationService"
159-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
160            android:directBootAware="true"
160-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
161            android:exported="false" />
161-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\a62d4e13a02f69be72dd1a09f131ea3b\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
162
163        <provider
163-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
164            android:name="androidx.startup.InitializationProvider"
164-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
165            android:authorities="me.wcy.music.androidx-startup"
165-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
166            android:exported="false" >
166-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
167            <meta-data
167-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
168                android:name="androidx.emoji2.text.EmojiCompatInitializer"
168-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
169                android:value="androidx.startup" />
169-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\487478292e3e6e14c6a27f95ada9fa66\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
170            <meta-data
170-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
171                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
171-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
172                android:value="androidx.startup" />
172-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b0c6e7adfd372a060b5489a22133f14c\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
173            <meta-data
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
174                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
175                android:value="androidx.startup" />
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
176        </provider>
177
178        <uses-library
178-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
179            android:name="androidx.window.extensions"
179-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
180            android:required="false" />
180-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
181        <uses-library
181-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
182            android:name="androidx.window.sidecar"
182-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
183            android:required="false" />
183-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\767165226d4846d17727bd190d952353\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
184
185        <provider
185-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:8:9-12:43
186            android:name="me.wcy.router.RouterProvider"
186-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:9:13-56
187            android:authorities="me.wcy.music.crouter.provider"
187-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:10:13-68
188            android:exported="false"
188-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:11:13-37
189            android:multiprocess="true" />
189-->[com.github.wangchenyan.crouter:crouter-api:3.0.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-4\b7ed2910bedb46e3efab1cd90ea6aec9\transformed\jetified-crouter-api-3.0.0-beta01\AndroidManifest.xml:12:13-40
190
191        <receiver
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
192            android:name="androidx.profileinstaller.ProfileInstallReceiver"
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
193            android:directBootAware="false"
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
194            android:enabled="true"
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
195            android:exported="true"
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
196            android:permission="android.permission.DUMP" >
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
197            <intent-filter>
197-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
198                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
199            </intent-filter>
200            <intent-filter>
200-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
201                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
202            </intent-filter>
203            <intent-filter>
203-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
204                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
204-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
205            </intent-filter>
206            <intent-filter>
206-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
207                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
207-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\7de980c485e621768064382eff0afa31\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
208            </intent-filter>
209        </receiver>
210
211        <provider
211-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:10:9-14:43
212            android:name="com.qw.soul.permission.InitProvider"
212-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:11:13-63
213            android:authorities="me.wcy.music.permission.provider"
213-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:12:13-71
214            android:exported="false"
214-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:13:13-37
215            android:multiprocess="true" />
215-->[com.github.soulqw:SoulPermission:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\2cd48bdea82363edfe078b1fc7ceb77b\transformed\jetified-SoulPermission-1.4.0\AndroidManifest.xml:14:13-40
216
217        <service android:name="com.liulishuo.filedownloader.services.FileDownloadService$SharedMainProcessService" />
217-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:9-118
217-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:12:18-115
218        <service
218-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:13:9-15:49
219            android:name="com.liulishuo.filedownloader.services.FileDownloadService$SeparateProcessService"
219-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:14:13-108
220            android:process=":filedownloader" />
220-->[com.liulishuo.filedownloader:library:1.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\5b3b292ae8cd49ed7f53f6be56e5417c\transformed\jetified-library-1.7.7\AndroidManifest.xml:15:13-46
221    </application>
222
223</manifest>
