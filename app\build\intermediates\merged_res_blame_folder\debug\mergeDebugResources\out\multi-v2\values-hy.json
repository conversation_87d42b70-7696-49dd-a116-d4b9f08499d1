{"logs": [{"outputFile": "me.wcy.music.app-mergeDebugResources-66:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\be9ab8b566fc36ab0e145ee170d21c48\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,190,254,322,403,480,554,631", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "122,185,249,317,398,475,549,626,704"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8945,9017,9080,9144,9212,9293,9370,9444,9521", "endColumns": "71,62,63,67,80,76,73,76,77", "endOffsets": "9012,9075,9139,9207,9288,9365,9439,9516,9594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\31112261c7df96d69093b9be7b3f632a\\transformed\\jetified-media3-session-1.4.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,244,320,436,539,628,706,790,880,978,1046,1161,1260,1362,1439,1533,1615,1717,1790,1861,1936,2015,2105", "endColumns": "80,107,75,115,102,88,77,83,89,97,67,114,98,101,76,93,81,101,72,70,74,78,89,93", "endOffsets": "131,239,315,431,534,623,701,785,875,973,1041,1156,1255,1357,1434,1528,1610,1712,1785,1856,1931,2010,2100,2194"}, "to": {"startLines": "66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5138,5344,5452,5528,5644,5747,5836,5914,5998,6088,6186,6254,6369,6468,6570,6647,6741,6823,11967,12040,12111,12186,12265,12355", "endColumns": "80,107,75,115,102,88,77,83,89,97,67,114,98,101,76,93,81,101,72,70,74,78,89,93", "endOffsets": "5214,5447,5523,5639,5742,5831,5909,5993,6083,6181,6249,6364,6463,6565,6642,6736,6818,6920,12035,12106,12181,12260,12350,12444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\286503f715fac5b6668bc7afcac59c2c\\transformed\\jetified-media3-ui-1.4.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,477,659,742,824,894,985,1081,1157,1220,1321,1424,1494,1562,1630,1696,1818,1934,2054,2118,2199,2276,2354,2450,2545,2614,2679,2732,2792,2840,2901,2969,3037,3110,3177,3238,3299,3366,3418,3480,3556,3632,3685", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,121,115,119,63,80,76,77,95,94,68,64,52,59,47,60,67,67,72,66,60,60,66,51,61,75,75,52,64", "endOffsets": "283,472,654,737,819,889,980,1076,1152,1215,1316,1419,1489,1557,1625,1691,1813,1929,2049,2113,2194,2271,2349,2445,2540,2609,2674,2727,2787,2835,2896,2964,3032,3105,3172,3233,3294,3361,3413,3475,3551,3627,3680,3745"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,383,572,6925,7008,7090,7160,7251,7347,7423,7486,7587,7690,7760,7828,7896,7962,8084,8200,8320,8384,8465,8542,8620,8716,8811,8880,9599,9652,9712,9760,9821,9889,9957,10030,10097,10158,10219,10286,10338,10400,10476,10552,10605", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "endColumns": "17,12,12,82,81,69,90,95,75,62,100,102,69,67,67,65,121,115,119,63,80,76,77,95,94,68,64,52,59,47,60,67,67,72,66,60,60,66,51,61,75,75,52,64", "endOffsets": "378,567,749,7003,7085,7155,7246,7342,7418,7481,7582,7685,7755,7823,7891,7957,8079,8195,8315,8379,8460,8537,8615,8711,8806,8875,8940,9647,9707,9755,9816,9884,9952,10025,10092,10153,10214,10281,10333,10395,10471,10547,10600,10665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\40b0160c565c3675e15a0634390e90ad\\transformed\\preference-1.2.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,346,483,652,736", "endColumns": "71,87,80,136,168,83,80", "endOffsets": "172,260,341,478,647,731,812"}, "to": {"startLines": "65,136,204,206,212,213,214", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5066,10670,16140,16306,16864,17033,17117", "endColumns": "71,87,80,136,168,83,80", "endOffsets": "5133,10753,16216,16438,17028,17112,17193"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8110c868e8d28496c81ffd9e11046d18\\transformed\\appcompat-1.7.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "919,1027,1127,1237,1326,1432,1549,1631,1711,1802,1895,1990,2084,2184,2277,2372,2466,2557,2648,2731,2837,2943,3042,3152,3260,3361,3531,16443", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "1022,1122,1232,1321,1427,1544,1626,1706,1797,1890,1985,2079,2179,2272,2367,2461,2552,2643,2726,2832,2938,3037,3147,3255,3356,3526,3623,16521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9151924b14ac859a0a182a0b1a1df226\\transformed\\core-1.13.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "55,56,57,58,59,60,61,211", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4040,4140,4245,4343,4442,4547,4649,16763", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "4135,4240,4338,4437,4542,4644,4755,16859"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ed9553fb9af75b90a85668bb0aac2cdb\\transformed\\material-1.12.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,589,677,772,902,983,1044,1108,1205,1290,1352,1439,1501,1565,1626,1693,1754,1808,1930,1987,2047,2101,2182,2317,2401,2477,2567,2646,2731,2867,2942,3017,3160,3255,3335,3391,3444,3510,3584,3663,3734,3817,3888,3964,4040,4117,4223,4311,4391,4487,4583,4657,4735,4835,4886,4970,5039,5126,5217,5279,5343,5406,5477,5582,5688,5788,5891,5951,6008,6093,6176,6250", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,75,79,91,87,94,129,80,60,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84,82,73,79", "endOffsets": "260,336,412,492,584,672,767,897,978,1039,1103,1200,1285,1347,1434,1496,1560,1621,1688,1749,1803,1925,1982,2042,2096,2177,2312,2396,2472,2562,2641,2726,2862,2937,3012,3155,3250,3330,3386,3439,3505,3579,3658,3729,3812,3883,3959,4035,4112,4218,4306,4386,4482,4578,4652,4730,4830,4881,4965,5034,5121,5212,5274,5338,5401,5472,5577,5683,5783,5886,5946,6003,6088,6171,6245,6325"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,67,68,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,205,208,209,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "754,3628,3704,3780,3860,3952,4760,4855,4985,5219,5280,10758,10855,10940,11002,11089,11151,11215,11276,11343,11404,11458,11580,11637,11697,11751,11832,12449,12533,12609,12699,12778,12863,12999,13074,13149,13292,13387,13467,13523,13576,13642,13716,13795,13866,13949,14020,14096,14172,14249,14355,14443,14523,14619,14715,14789,14867,14967,15018,15102,15171,15258,15349,15411,15475,15538,15609,15714,15820,15920,16023,16083,16221,16526,16609,16683", "endLines": "22,50,51,52,53,54,62,63,64,67,68,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,205,208,209,210", "endColumns": "12,75,75,79,91,87,94,129,80,60,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,75,89,78,84,135,74,74,142,94,79,55,52,65,73,78,70,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84,82,73,79", "endOffsets": "914,3699,3775,3855,3947,4035,4850,4980,5061,5275,5339,10850,10935,10997,11084,11146,11210,11271,11338,11399,11453,11575,11632,11692,11746,11827,11962,12528,12604,12694,12773,12858,12994,13069,13144,13287,13382,13462,13518,13571,13637,13711,13790,13861,13944,14015,14091,14167,14244,14350,14438,14518,14614,14710,14784,14862,14962,15013,15097,15166,15253,15344,15406,15470,15533,15604,15709,15815,15915,16018,16078,16135,16301,16604,16678,16758"}}]}]}