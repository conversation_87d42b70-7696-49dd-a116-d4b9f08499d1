{"logs": [{"outputFile": "me.wcy.music.app-mergeDebugResources-66:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\be9ab8b566fc36ab0e145ee170d21c48\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,268,334,409,475,574,670", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "121,186,263,329,404,470,569,665,750"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8783,8854,8919,8996,9062,9137,9203,9302,9398", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "8849,8914,8991,9057,9132,9198,9297,9393,9478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\31112261c7df96d69093b9be7b3f632a\\transformed\\jetified-media3-session-1.4.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,134,229,297,405,502,588,673,761,866,959,1031,1126,1204,1305,1392,1484,1560,1658,1725,1791,1869,1948,2036", "endColumns": "78,94,67,107,96,85,84,87,104,92,71,94,77,100,86,91,75,97,66,65,77,78,87,92", "endOffsets": "129,224,292,400,497,583,668,756,861,954,1026,1121,1199,1300,1387,1479,1555,1653,1720,1786,1864,1943,2031,2124"}, "to": {"startLines": "66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5120,5319,5414,5482,5590,5687,5773,5858,5946,6051,6144,6216,6311,6389,6490,6577,6669,6745,11758,11825,11891,11969,12048,12136", "endColumns": "78,94,67,107,96,85,84,87,104,92,71,94,77,100,86,91,75,97,66,65,77,78,87,92", "endOffsets": "5194,5409,5477,5585,5682,5768,5853,5941,6046,6139,6211,6306,6384,6485,6572,6664,6740,6838,11820,11886,11964,12043,12131,12224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\286503f715fac5b6668bc7afcac59c2c\\transformed\\jetified-media3-ui-1.4.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,487,667,756,843,926,1017,1111,1182,1245,1336,1427,1491,1554,1614,1682,1790,1907,2020,2090,2166,2237,2308,2394,2478,2544,2607,2660,2718,2766,2827,2887,2959,3021,3083,3144,3206,3271,3323,3383,3457,3531,3583", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,107,116,112,69,75,70,70,85,83,65,62,52,57,47,60,59,71,61,61,60,61,64,51,59,73,73,51,63", "endOffsets": "279,482,662,751,838,921,1012,1106,1177,1240,1331,1422,1486,1549,1609,1677,1785,1902,2015,2085,2161,2232,2303,2389,2473,2539,2602,2655,2713,2761,2822,2882,2954,3016,3078,3139,3201,3266,3318,3378,3452,3526,3578,3642"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,379,582,6843,6932,7019,7102,7193,7287,7358,7421,7512,7603,7667,7730,7790,7858,7966,8083,8196,8266,8342,8413,8484,8570,8654,8720,9483,9536,9594,9642,9703,9763,9835,9897,9959,10020,10082,10147,10199,10259,10333,10407,10459", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,107,116,112,69,75,70,70,85,83,65,62,52,57,47,60,59,71,61,61,60,61,64,51,59,73,73,51,63", "endOffsets": "374,577,757,6927,7014,7097,7188,7282,7353,7416,7507,7598,7662,7725,7785,7853,7961,8078,8191,8261,8337,8408,8479,8565,8649,8715,8778,9531,9589,9637,9698,9758,9830,9892,9954,10015,10077,10142,10194,10254,10328,10402,10454,10518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9151924b14ac859a0a182a0b1a1df226\\transformed\\core-1.13.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "55,56,57,58,59,60,61,211", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4012,4110,4212,4315,4416,4518,4616,16468", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "4105,4207,4310,4411,4513,4611,4740,16564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\40b0160c565c3675e15a0634390e90ad\\transformed\\preference-1.2.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,262,341,488,657,737", "endColumns": "71,84,78,146,168,79,77", "endOffsets": "172,257,336,483,652,732,810"}, "to": {"startLines": "65,136,204,206,212,213,214", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5048,10523,15851,16007,16569,16738,16818", "endColumns": "71,84,78,146,168,79,77", "endOffsets": "5115,10603,15925,16149,16733,16813,16891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8110c868e8d28496c81ffd9e11046d18\\transformed\\appcompat-1.7.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1028,1125,1230,1316,1416,1529,1607,1684,1775,1868,1962,2056,2156,2249,2344,2438,2529,2620,2699,2809,2912,3008,3119,3221,3331,3490,16154", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "1023,1120,1225,1311,1411,1524,1602,1679,1770,1863,1957,2051,2151,2244,2339,2433,2524,2615,2694,2804,2907,3003,3114,3216,3326,3485,3582,16229"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ed9553fb9af75b90a85668bb0aac2cdb\\transformed\\material-1.12.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1047,1111,1203,1272,1331,1416,1479,1541,1599,1663,1724,1778,1892,1950,2010,2064,2134,2261,2342,2432,2531,2628,2707,2842,2918,2995,3124,3208,3290,3345,3400,3466,3535,3612,3683,3762,3830,3906,3976,4041,4143,4238,4311,4405,4498,4572,4641,4735,4791,4874,4941,5025,5113,5175,5239,5302,5369,5466,5572,5663,5765,5824,5883,5960,6045,6121", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "258,335,414,495,594,683,791,903,986,1042,1106,1198,1267,1326,1411,1474,1536,1594,1658,1719,1773,1887,1945,2005,2059,2129,2256,2337,2427,2526,2623,2702,2837,2913,2990,3119,3203,3285,3340,3395,3461,3530,3607,3678,3757,3825,3901,3971,4036,4138,4233,4306,4400,4493,4567,4636,4730,4786,4869,4936,5020,5108,5170,5234,5297,5364,5461,5567,5658,5760,5819,5878,5955,6040,6116,6189"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,67,68,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,205,208,209,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "762,3587,3664,3743,3824,3923,4745,4853,4965,5199,5255,10608,10700,10769,10828,10913,10976,11038,11096,11160,11221,11275,11389,11447,11507,11561,11631,12229,12310,12400,12499,12596,12675,12810,12886,12963,13092,13176,13258,13313,13368,13434,13503,13580,13651,13730,13798,13874,13944,14009,14111,14206,14279,14373,14466,14540,14609,14703,14759,14842,14909,14993,15081,15143,15207,15270,15337,15434,15540,15631,15733,15792,15930,16234,16319,16395", "endLines": "22,50,51,52,53,54,62,63,64,67,68,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,205,208,209,210", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "920,3659,3738,3819,3918,4007,4848,4960,5043,5250,5314,10695,10764,10823,10908,10971,11033,11091,11155,11216,11270,11384,11442,11502,11556,11626,11753,12305,12395,12494,12591,12670,12805,12881,12958,13087,13171,13253,13308,13363,13429,13498,13575,13646,13725,13793,13869,13939,14004,14106,14201,14274,14368,14461,14535,14604,14698,14754,14837,14904,14988,15076,15138,15202,15265,15332,15429,15535,15626,15728,15787,15846,16002,16314,16390,16463"}}]}]}