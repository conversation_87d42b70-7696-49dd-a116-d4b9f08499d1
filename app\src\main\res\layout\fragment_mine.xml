<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <top.wangchenyan.common.widget.TitleLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tlShowBack="false"
        app:tlTitleText="" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="16dp"
            android:paddingBottom="16dp">

            <FrameLayout
                android:id="@+id/flProfile"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="32dp"
                    android:background="@drawable/bg_card"
                    android:gravity="center_horizontal"
                    android:orientation="vertical"
                    android:paddingTop="32dp"
                    android:paddingBottom="16dp">

                    <TextView
                        android:id="@+id/tvNickName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:hint="立即登录"
                        android:textColor="@color/common_text_h1_color"
                        android:textColorHint="@color/common_text_h1_color"
                        android:textSize="18dp"
                        android:textStyle="bold" />
                </LinearLayout>

                <ImageView
                    android:id="@+id/ivAvatar"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/ic_launcher_round" />
            </FrameLayout>

            <LinearLayout
                android:id="@+id/localMusic"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_card"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:drawableStart="@drawable/ic_local_music"
                    android:drawablePadding="8dp"
                    android:drawableTint="@color/common_theme_color"
                    android:gravity="center_vertical"
                    android:text="本地音乐"
                    android:textColor="@color/common_text_h1_color"
                    android:textSize="16dp" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_arrow_right"
                    app:tint="@color/common_text_h2_color" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llLikePlaylist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_card"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvLikePlaylist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="16dp"
                    android:nestedScrollingEnabled="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="1"
                    tools:listitem="@layout/item_user_playlist" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llMyPlaylist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_card"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvMyPlaylist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:paddingHorizontal="16dp"
                    android:text="创建歌单"
                    android:textColor="@color/common_text_h2_color"
                    android:textSize="12dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvMyPlaylist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="12dp"
                    android:nestedScrollingEnabled="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="2"
                    tools:listitem="@layout/item_user_playlist" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llCollectPlaylist"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/bg_card"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvCollectPlaylist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:paddingHorizontal="16dp"
                    android:text="收藏歌单"
                    android:textColor="@color/common_text_h2_color"
                    android:textSize="12dp" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvCollectPlaylist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="12dp"
                    android:nestedScrollingEnabled="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="2"
                    tools:listitem="@layout/item_user_playlist" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>