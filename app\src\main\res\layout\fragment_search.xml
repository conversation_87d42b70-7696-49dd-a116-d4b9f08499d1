<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <top.wangchenyan.common.widget.TitleLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tlTitleContentLayout="@layout/title_search" />

    <FrameLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:id="@+id/llResult"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayout"
                style="@style/TabLayout"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                app:tabMode="fixed" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/viewPage2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="5dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llHistory"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingHorizontal="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="搜索历史"
                android:textColor="@color/common_text_h1_color"
                android:textSize="16dp"
                android:textStyle="bold" />

            <com.google.android.flexbox.FlexboxLayout
                android:id="@+id/flHistory"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:dividerDrawable="@drawable/common_ic_divider_flexbox_6"
                app:showDivider="middle" />
        </LinearLayout>
    </FrameLayout>

    <me.wcy.music.widget.PlayBar
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>