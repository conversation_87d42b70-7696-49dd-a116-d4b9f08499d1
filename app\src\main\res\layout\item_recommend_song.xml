<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingVertical="10dp">

    <ImageView
        android:id="@+id/ivCover"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_marginStart="16dp"
        android:scaleType="fitXY"
        android:src="@drawable/ic_default_cover" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="歌曲"
            android:textColor="@color/common_text_h1_color"
            android:textSize="16dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:divider="@drawable/common_ic_divider_flexbox_4"
            android:orientation="horizontal"
            android:showDividers="middle">

            <com.hjq.shape.view.ShapeTextView
                android:id="@+id/tvTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="3dp"
                android:textSize="10dp"
                app:shape_radius="4dp"
                app:shape_solidColor="@color/red_500_p30"
                app:shape_textColor="@color/red_500"
                tools:text="万人评论" />

            <TextView
                android:id="@+id/tvSubTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:singleLine="true"
                android:text="歌手 - 专辑"
                android:textColor="@color/grey"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/ivMore"
        android:layout_width="40dp"
        android:layout_height="match_parent"
        android:contentDescription="@null"
        android:scaleType="centerInside"
        android:src="@drawable/ic_more"
        app:tint="@color/common_text_h2_color" />
</LinearLayout>