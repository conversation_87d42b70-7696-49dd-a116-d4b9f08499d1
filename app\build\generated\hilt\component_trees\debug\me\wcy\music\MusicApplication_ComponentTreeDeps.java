package me.wcy.music;

import dagger.hilt.internal.aggregatedroot.codegen._me_wcy_music_MusicApplication;
import dagger.hilt.internal.componenttreedeps.ComponentTreeDeps;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ActivityComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ActivityRetainedComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_FragmentComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ServiceComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewModelComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewWithFragmentComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ActivityComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ActivityRetainedComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_FragmentComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ServiceComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewModelComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewWithFragmentComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_components_SingletonComponent;
import hilt_aggregated_deps._dagger_hilt_android_flags_FragmentGetContextFix_FragmentGetContextFixEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_flags_HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_ActivityEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_FragmentEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltViewModelFactory_ViewModelFactoriesEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_DefaultViewModelFactories_ActivityModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ViewModelModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ActivityComponentManager_ActivityComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_FragmentComponentManager_FragmentComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_LifecycleModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ServiceComponentManager_ServiceComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ViewComponentManager_ViewComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ViewComponentManager_ViewWithFragmentComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_modules_ApplicationContextModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_modules_HiltWrapper_ActivityModule;
import hilt_aggregated_deps._me_wcy_music_MusicApplication_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_account_login_phone_PhoneLoginFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_account_login_phone_PhoneLoginViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._me_wcy_music_account_login_phone_PhoneLoginViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._me_wcy_music_account_login_qrcode_QrcodeLoginFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_account_login_qrcode_QrcodeLoginViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._me_wcy_music_account_login_qrcode_QrcodeLoginViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._me_wcy_music_account_service_UserServiceModule;
import hilt_aggregated_deps._me_wcy_music_account_service_UserServiceModule_UserServiceEntryPoint;
import hilt_aggregated_deps._me_wcy_music_common_MusicFragmentContainerActivity_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_discover_home_DiscoverFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_discover_home_viewmodel_DiscoverViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._me_wcy_music_discover_home_viewmodel_DiscoverViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._me_wcy_music_discover_playlist_detail_PlaylistDetailFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_discover_playlist_detail_viewmodel_PlaylistViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._me_wcy_music_discover_playlist_detail_viewmodel_PlaylistViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._me_wcy_music_discover_playlist_square_PlaylistSquareFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_discover_playlist_square_PlaylistTabFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_discover_ranking_RankingFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_discover_recommend_song_RecommendSongFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_main_MainActivity_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_main_SettingsActivity_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_main_SettingsActivity_SettingsFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_main_playing_PlayingActivity_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_main_playlist_CurrentPlaylistFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_mine_collect_song_CollectSongFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_mine_collect_song_CollectSongViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._me_wcy_music_mine_collect_song_CollectSongViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._me_wcy_music_mine_home_MineFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_mine_home_viewmodel_MineViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._me_wcy_music_mine_home_viewmodel_MineViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._me_wcy_music_mine_local_LocalMusicFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_search_SearchFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_search_playlist_SearchPlaylistFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_search_song_SearchSongFragment_GeneratedInjector;
import hilt_aggregated_deps._me_wcy_music_service_PlayServiceModule;
import hilt_aggregated_deps._me_wcy_music_service_PlayServiceModule_PlayerControllerEntryPoint;
import hilt_aggregated_deps._me_wcy_music_service_likesong_LikeSongProcessorModule;
import hilt_aggregated_deps._me_wcy_music_service_likesong_LikeSongProcessorModule_LikeSongProcessorEntryPoint;
import hilt_aggregated_deps._me_wcy_music_storage_db_DatabaseModule;

@ComponentTreeDeps(
    rootDeps = _me_wcy_music_MusicApplication.class,
    defineComponentDeps = {
        _dagger_hilt_android_components_ActivityComponent.class,
        _dagger_hilt_android_components_ActivityRetainedComponent.class,
        _dagger_hilt_android_components_FragmentComponent.class,
        _dagger_hilt_android_components_ServiceComponent.class,
        _dagger_hilt_android_components_ViewComponent.class,
        _dagger_hilt_android_components_ViewModelComponent.class,
        _dagger_hilt_android_components_ViewWithFragmentComponent.class,
        _dagger_hilt_android_internal_builders_ActivityComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ActivityRetainedComponentBuilder.class,
        _dagger_hilt_android_internal_builders_FragmentComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ServiceComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewModelComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewWithFragmentComponentBuilder.class,
        _dagger_hilt_components_SingletonComponent.class
    },
    aggregatedDeps = {
        _dagger_hilt_android_flags_FragmentGetContextFix_FragmentGetContextFixEntryPoint.class,
        _dagger_hilt_android_flags_HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule.class,
        _dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_ActivityEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_FragmentEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltViewModelFactory_ViewModelFactoriesEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_DefaultViewModelFactories_ActivityModule.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ViewModelModule.class,
        _dagger_hilt_android_internal_managers_ActivityComponentManager_ActivityComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_FragmentComponentManager_FragmentComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_LifecycleModule.class,
        _dagger_hilt_android_internal_managers_ServiceComponentManager_ServiceComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_ViewComponentManager_ViewComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_ViewComponentManager_ViewWithFragmentComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_modules_ApplicationContextModule.class,
        _dagger_hilt_android_internal_modules_HiltWrapper_ActivityModule.class,
        _me_wcy_music_MusicApplication_GeneratedInjector.class,
        _me_wcy_music_account_login_phone_PhoneLoginFragment_GeneratedInjector.class,
        _me_wcy_music_account_login_phone_PhoneLoginViewModel_HiltModules_BindsModule.class,
        _me_wcy_music_account_login_phone_PhoneLoginViewModel_HiltModules_KeyModule.class,
        _me_wcy_music_account_login_qrcode_QrcodeLoginFragment_GeneratedInjector.class,
        _me_wcy_music_account_login_qrcode_QrcodeLoginViewModel_HiltModules_BindsModule.class,
        _me_wcy_music_account_login_qrcode_QrcodeLoginViewModel_HiltModules_KeyModule.class,
        _me_wcy_music_account_service_UserServiceModule.class,
        _me_wcy_music_account_service_UserServiceModule_UserServiceEntryPoint.class,
        _me_wcy_music_common_MusicFragmentContainerActivity_GeneratedInjector.class,
        _me_wcy_music_discover_home_DiscoverFragment_GeneratedInjector.class,
        _me_wcy_music_discover_home_viewmodel_DiscoverViewModel_HiltModules_BindsModule.class,
        _me_wcy_music_discover_home_viewmodel_DiscoverViewModel_HiltModules_KeyModule.class,
        _me_wcy_music_discover_playlist_detail_PlaylistDetailFragment_GeneratedInjector.class,
        _me_wcy_music_discover_playlist_detail_viewmodel_PlaylistViewModel_HiltModules_BindsModule.class,
        _me_wcy_music_discover_playlist_detail_viewmodel_PlaylistViewModel_HiltModules_KeyModule.class,
        _me_wcy_music_discover_playlist_square_PlaylistSquareFragment_GeneratedInjector.class,
        _me_wcy_music_discover_playlist_square_PlaylistTabFragment_GeneratedInjector.class,
        _me_wcy_music_discover_ranking_RankingFragment_GeneratedInjector.class,
        _me_wcy_music_discover_recommend_song_RecommendSongFragment_GeneratedInjector.class,
        _me_wcy_music_main_MainActivity_GeneratedInjector.class,
        _me_wcy_music_main_SettingsActivity_GeneratedInjector.class,
        _me_wcy_music_main_SettingsActivity_SettingsFragment_GeneratedInjector.class,
        _me_wcy_music_main_playing_PlayingActivity_GeneratedInjector.class,
        _me_wcy_music_main_playlist_CurrentPlaylistFragment_GeneratedInjector.class,
        _me_wcy_music_mine_collect_song_CollectSongFragment_GeneratedInjector.class,
        _me_wcy_music_mine_collect_song_CollectSongViewModel_HiltModules_BindsModule.class,
        _me_wcy_music_mine_collect_song_CollectSongViewModel_HiltModules_KeyModule.class,
        _me_wcy_music_mine_home_MineFragment_GeneratedInjector.class,
        _me_wcy_music_mine_home_viewmodel_MineViewModel_HiltModules_BindsModule.class,
        _me_wcy_music_mine_home_viewmodel_MineViewModel_HiltModules_KeyModule.class,
        _me_wcy_music_mine_local_LocalMusicFragment_GeneratedInjector.class,
        _me_wcy_music_search_SearchFragment_GeneratedInjector.class,
        _me_wcy_music_search_playlist_SearchPlaylistFragment_GeneratedInjector.class,
        _me_wcy_music_search_song_SearchSongFragment_GeneratedInjector.class,
        _me_wcy_music_service_PlayServiceModule.class,
        _me_wcy_music_service_PlayServiceModule_PlayerControllerEntryPoint.class,
        _me_wcy_music_service_likesong_LikeSongProcessorModule.class,
        _me_wcy_music_service_likesong_LikeSongProcessorModule_LikeSongProcessorEntryPoint.class,
        _me_wcy_music_storage_db_DatabaseModule.class
    }
)
public final class MusicApplication_ComponentTreeDeps {
}
