{"logs": [{"outputFile": "me.wcy.music.app-mergeDebugResources-66:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9151924b14ac859a0a182a0b1a1df226\\transformed\\core-1.13.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "58,59,60,61,62,63,64,214", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4356,4454,4556,4656,4755,4857,4966,17134", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "4449,4551,4651,4750,4852,4961,5078,17230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\40b0160c565c3675e15a0634390e90ad\\transformed\\preference-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,337,476,645,732", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "171,257,332,471,640,727,808"}, "to": {"startLines": "68,139,207,209,215,216,217", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5389,11006,16517,16671,17235,17404,17491", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "5455,11087,16587,16805,17399,17486,17567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8110c868e8d28496c81ffd9e11046d18\\transformed\\appcompat-1.7.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,334,447,531,636,755,840,920,1011,1104,1199,1293,1393,1486,1581,1675,1766,1858,1939,2049,2157,2255,2367,2473,2577,2739,2840", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "223,329,442,526,631,750,835,915,1006,1099,1194,1288,1388,1481,1576,1670,1761,1853,1934,2044,2152,2250,2362,2468,2572,2734,2835,2917"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1168,1291,1397,1510,1594,1699,1818,1903,1983,2074,2167,2262,2356,2456,2549,2644,2738,2829,2921,3002,3112,3220,3318,3430,3536,3640,3802,16810", "endColumns": "122,105,112,83,104,118,84,79,90,92,94,93,99,92,94,93,90,91,80,109,107,97,111,105,103,161,100,81", "endOffsets": "1286,1392,1505,1589,1694,1813,1898,1978,2069,2162,2257,2351,2451,2544,2639,2733,2824,2916,2997,3107,3215,3313,3425,3531,3635,3797,3898,16887"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\31112261c7df96d69093b9be7b3f632a\\transformed\\jetified-media3-session-1.4.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,235,313,416,506,594,684,777,875,971,1043,1147,1246,1339,1413,1509,1593,1682,1754,1820,1903,1990,2086", "endColumns": "74,104,77,102,89,87,89,92,97,95,71,103,98,92,73,95,83,88,71,65,82,86,95,100", "endOffsets": "125,230,308,411,501,589,679,772,870,966,1038,1142,1241,1334,1408,1504,1588,1677,1749,1815,1898,1985,2081,2182"}, "to": {"startLines": "69,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,156,157,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5460,5663,5768,5846,5949,6039,6127,6217,6310,6408,6504,6576,6680,6779,6872,6946,7042,7126,12295,12367,12433,12516,12603,12699", "endColumns": "74,104,77,102,89,87,89,92,97,95,71,103,98,92,73,95,83,88,71,65,82,86,95,100", "endOffsets": "5530,5763,5841,5944,6034,6122,6212,6305,6403,6499,6571,6675,6774,6867,6941,7037,7121,7210,12362,12428,12511,12598,12694,12795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ed9553fb9af75b90a85668bb0aac2cdb\\transformed\\material-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1142,1208,1303,1377,1437,1521,1583,1649,1707,1780,1843,1899,2018,2075,2136,2192,2266,2411,2497,2572,2661,2740,2824,2957,3039,3122,3268,3358,3438,3493,3544,3610,3683,3761,3832,3917,3988,4065,4139,4211,4317,4408,4482,4577,4675,4749,4829,4930,4983,5069,5135,5224,5314,5376,5440,5503,5577,5689,5799,5909,6014,6073,6128,6207,6293,6370", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1137,1203,1298,1372,1432,1516,1578,1644,1702,1775,1838,1894,2013,2070,2131,2187,2261,2406,2492,2567,2656,2735,2819,2952,3034,3117,3263,3353,3433,3488,3539,3605,3678,3756,3827,3912,3983,4060,4134,4206,4312,4403,4477,4572,4670,4744,4824,4925,4978,5064,5130,5219,5309,5371,5435,5498,5572,5684,5794,5904,6009,6068,6123,6202,6288,6365,6444"}, "to": {"startLines": "21,53,54,55,56,57,65,66,67,70,71,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,208,211,212,213", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "947,3903,3995,4083,4170,4266,5083,5184,5305,5535,5597,11092,11187,11261,11321,11405,11467,11533,11591,11664,11727,11783,11902,11959,12020,12076,12150,12800,12886,12961,13050,13129,13213,13346,13428,13511,13657,13747,13827,13882,13933,13999,14072,14150,14221,14306,14377,14454,14528,14600,14706,14797,14871,14966,15064,15138,15218,15319,15372,15458,15524,15613,15703,15765,15829,15892,15966,16078,16188,16298,16403,16462,16592,16892,16978,17055", "endLines": "25,53,54,55,56,57,65,66,67,70,71,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,208,211,212,213", "endColumns": "12,91,87,86,95,89,100,120,83,61,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,74,88,78,83,132,81,82,145,89,79,54,50,65,72,77,70,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78,85,76,78", "endOffsets": "1163,3990,4078,4165,4261,4351,5179,5300,5384,5592,5658,11182,11256,11316,11400,11462,11528,11586,11659,11722,11778,11897,11954,12015,12071,12145,12290,12881,12956,13045,13124,13208,13341,13423,13506,13652,13742,13822,13877,13928,13994,14067,14145,14216,14301,14372,14449,14523,14595,14701,14792,14866,14961,15059,15133,15213,15314,15367,15453,15519,15608,15698,15760,15824,15887,15961,16073,16183,16293,16398,16457,16512,16666,16973,17050,17129"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\be9ab8b566fc36ab0e145ee170d21c48\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,198,263,335,413,493,583,676", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "131,193,258,330,408,488,578,671,745"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9245,9326,9388,9453,9525,9603,9683,9773,9866", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "9321,9383,9448,9520,9598,9678,9768,9861,9935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\286503f715fac5b6668bc7afcac59c2c\\transformed\\jetified-media3-ui-1.4.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,940,1030,1119,1216,1310,1385,1451,1548,1646,1715,1778,1841,1910,2024,2137,2251,2328,2408,2477,2553,2652,2753,2819,2882,2935,2993,3041,3102,3166,3236,3301,3370,3431,3489,3555,3607,3669,3745,3821,3878", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,51,61,75,75,56,69", "endOffsets": "281,577,847,935,1025,1114,1211,1305,1380,1446,1543,1641,1710,1773,1836,1905,2019,2132,2246,2323,2403,2472,2548,2647,2748,2814,2877,2930,2988,3036,3097,3161,3231,3296,3365,3426,3484,3550,3602,3664,3740,3816,3873,3943"}, "to": {"startLines": "2,11,16,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,677,7215,7303,7393,7482,7579,7673,7748,7814,7911,8009,8078,8141,8204,8273,8387,8500,8614,8691,8771,8840,8916,9015,9116,9182,9940,9993,10051,10099,10160,10224,10294,10359,10428,10489,10547,10613,10665,10727,10803,10879,10936", "endLines": "10,15,20,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,51,61,75,75,56,69", "endOffsets": "376,672,942,7298,7388,7477,7574,7668,7743,7809,7906,8004,8073,8136,8199,8268,8382,8495,8609,8686,8766,8835,8911,9010,9111,9177,9240,9988,10046,10094,10155,10219,10289,10354,10423,10484,10542,10608,10660,10722,10798,10874,10931,11001"}}]}]}