{"logs": [{"outputFile": "me.wcy.music.app-mergeDebugResources-66:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\40b0160c565c3675e15a0634390e90ad\\transformed\\preference-1.2.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "65,136,204,206,212,213,214", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4998,10297,15571,15723,16272,16441,16521", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "5061,10379,15640,15852,16436,16516,16592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ed9553fb9af75b90a85668bb0aac2cdb\\transformed\\material-1.12.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,777,892,971,1031,1096,1186,1253,1312,1402,1466,1530,1593,1662,1726,1780,1892,1950,2012,2066,2138,2260,2347,2422,2513,2594,2675,2815,2892,2973,3100,3191,3268,3322,3373,3439,3509,3586,3657,3732,3803,3880,3949,4018,4125,4216,4288,4377,4466,4540,4612,4698,4748,4827,4893,4973,5057,5119,5183,5246,5315,5415,5510,5602,5694,5752,5807,5885,5966,6041", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77,80,74,74", "endOffsets": "267,349,427,504,590,674,772,887,966,1026,1091,1181,1248,1307,1397,1461,1525,1588,1657,1721,1775,1887,1945,2007,2061,2133,2255,2342,2417,2508,2589,2670,2810,2887,2968,3095,3186,3263,3317,3368,3434,3504,3581,3652,3727,3798,3875,3944,4013,4120,4211,4283,4372,4461,4535,4607,4693,4743,4822,4888,4968,5052,5114,5178,5241,5310,5410,5505,5597,5689,5747,5802,5880,5961,6036,6111"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,67,68,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,205,208,209,210", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "751,3580,3662,3740,3817,3903,4706,4804,4919,5140,5200,10384,10474,10541,10600,10690,10754,10818,10881,10950,11014,11068,11180,11238,11300,11354,11426,12024,12111,12186,12277,12358,12439,12579,12656,12737,12864,12955,13032,13086,13137,13203,13273,13350,13421,13496,13567,13644,13713,13782,13889,13980,14052,14141,14230,14304,14376,14462,14512,14591,14657,14737,14821,14883,14947,15010,15079,15179,15274,15366,15458,15516,15645,15940,16021,16096", "endLines": "22,50,51,52,53,54,62,63,64,67,68,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,205,208,209,210", "endColumns": "12,81,77,76,85,83,97,114,78,59,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,74,90,80,80,139,76,80,126,90,76,53,50,65,69,76,70,74,70,76,68,68,106,90,71,88,88,73,71,85,49,78,65,79,83,61,63,62,68,99,94,91,91,57,54,77,80,74,74", "endOffsets": "918,3657,3735,3812,3898,3982,4799,4914,4993,5195,5260,10469,10536,10595,10685,10749,10813,10876,10945,11009,11063,11175,11233,11295,11349,11421,11543,12106,12181,12272,12353,12434,12574,12651,12732,12859,12950,13027,13081,13132,13198,13268,13345,13416,13491,13562,13639,13708,13777,13884,13975,14047,14136,14225,14299,14371,14457,14507,14586,14652,14732,14816,14878,14942,15005,15074,15174,15269,15361,15453,15511,15566,15718,16016,16091,16166"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\286503f715fac5b6668bc7afcac59c2c\\transformed\\jetified-media3-ui-1.4.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,656,738,820,898,985,1070,1137,1200,1292,1384,1449,1512,1574,1645,1755,1866,1976,2043,2123,2194,2261,2346,2431,2494,2558,2611,2669,2717,2778,2843,2905,2970,3041,3099,3157,3223,3275,3337,3413,3489,3543", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,51,61,75,75,53,65", "endOffsets": "280,474,651,733,815,893,980,1065,1132,1195,1287,1379,1444,1507,1569,1640,1750,1861,1971,2038,2118,2189,2256,2341,2426,2489,2553,2606,2664,2712,2773,2838,2900,2965,3036,3094,3152,3218,3270,3332,3408,3484,3538,3604"}, "to": {"startLines": "2,11,15,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,574,6697,6779,6861,6939,7026,7111,7178,7241,7333,7425,7490,7553,7615,7686,7796,7907,8017,8084,8164,8235,8302,8387,8472,8535,9246,9299,9357,9405,9466,9531,9593,9658,9729,9787,9845,9911,9963,10025,10101,10177,10231", "endLines": "10,14,18,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,51,61,75,75,53,65", "endOffsets": "375,569,746,6774,6856,6934,7021,7106,7173,7236,7328,7420,7485,7548,7610,7681,7791,7902,8012,8079,8159,8230,8297,8382,8467,8530,8594,9294,9352,9400,9461,9526,9588,9653,9724,9782,9840,9906,9958,10020,10096,10172,10226,10292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8110c868e8d28496c81ffd9e11046d18\\transformed\\appcompat-1.7.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,207", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "923,1027,1127,1235,1319,1419,1534,1612,1687,1778,1871,1966,2060,2160,2253,2348,2442,2533,2624,2706,2809,2912,3011,3116,3220,3324,3480,15857", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "1022,1122,1230,1314,1414,1529,1607,1682,1773,1866,1961,2055,2155,2248,2343,2437,2528,2619,2701,2804,2907,3006,3111,3215,3319,3475,3575,15935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\31112261c7df96d69093b9be7b3f632a\\transformed\\jetified-media3-session-1.4.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,218,291,387,482,565,649,731,817,902,966,1061,1141,1235,1309,1400,1472,1561,1629,1695,1768,1850,1940", "endColumns": "73,88,72,95,94,82,83,81,85,84,63,94,79,93,73,90,71,88,67,65,72,81,89,96", "endOffsets": "124,213,286,382,477,560,644,726,812,897,961,1056,1136,1230,1304,1395,1467,1556,1624,1690,1763,1845,1935,2032"}, "to": {"startLines": "66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,153,154,155,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5066,5265,5354,5427,5523,5618,5701,5785,5867,5953,6038,6102,6197,6277,6371,6445,6536,6608,11548,11616,11682,11755,11837,11927", "endColumns": "73,88,72,95,94,82,83,81,85,84,63,94,79,93,73,90,71,88,67,65,72,81,89,96", "endOffsets": "5135,5349,5422,5518,5613,5696,5780,5862,5948,6033,6097,6192,6272,6366,6440,6531,6603,6692,11611,11677,11750,11832,11922,12019"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\be9ab8b566fc36ab0e145ee170d21c48\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "8599,8669,8731,8796,8860,8937,9002,9092,9177", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "8664,8726,8791,8855,8932,8997,9087,9172,9241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\9151924b14ac859a0a182a0b1a1df226\\transformed\\core-1.13.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "55,56,57,58,59,60,61,211", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3987,4083,4185,4284,4383,4487,4590,16171", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "4078,4180,4279,4378,4482,4585,4701,16267"}}]}]}